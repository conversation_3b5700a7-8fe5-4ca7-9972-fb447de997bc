import dash
# from dash.dependencies import Input, Output
from dash import html, dcc, callback, Input, Output, State
from dash.exceptions import PreventUpdate
import plotly.io as pio
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from dash import dash_table
import os
from openai import AzureOpenAI
from db_connector import DatabaseConnector

dash.register_page(__name__, path='/')
pio.templates.default = "seaborn"

# --- Variables ---
co_df = None
po_df = None
use_csv = False
all_schools = [];
all_departments = [];
all_programs = [];

# --- Database Connection ---
# Replace with your actual credentials
# DB_SERVER = "20.242.124.131"
# DB_NAME = "ChitkaraPB_prodDB"
# DB_USER = "datafiReader"
# DB_PASSWORD = "P96Bry3rnGAv#9z8&f19tgS5reX4MU&T"

DB_SERVER = "MYHP"
DB_NAME = "ChitkaraPB_prodDB"
DB_USER = "sa"
DB_PASSWORD = "p@ssw0rd000"



try:
    df = pd.read_csv('pages/master-schools.csv')
except FileNotFoundError as e:
    print(f"Error: One or more data files not found: {e}")

master_school_df = df[['MasterSchoolName', 'MasterSchoolId', 'DBName']].drop_duplicates()
master_school_options = [
    {'label': row['MasterSchoolName'], 'value': row['MasterSchoolId'], 'dbname': row['DBName']} for _, row in master_school_df.iterrows()
]

# --- Data Loading ---
if use_csv:
    try:
        co_df = pd.read_csv('pages/gu_and_cimr_co_data.csv')
    except FileNotFoundError as e:
        co_df = pd.DataFrame()
        print(f"Error: One or more data files not found: {e}")

    # try:
    #     po_df = pd.read_csv('pages/GU_PO_attainment.csv')
    # except FileNotFoundError as e:
    #     print(f"Error: One or more data files not found: {e}")

else:

    # --- Database Connection ---
    # Replace with your actual credentials

    db_connector = DatabaseConnector(DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD)
    try:
        db_connector.connect()
        # --- Data Loading and Preprocessing ---
        co_query = "SELECT * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 3"
        # TOP (50000)
        # AND ProgramName = 'B. Pharmacy'
        co_df = db_connector.execute_query(co_query)
        co_df = co_df.rename(columns={'Attainmemt': 'Attainment'})
        if co_df is None:
            raise Exception("Failed to load CO data from database.")
        
        all_schools = co_df['SchoolName', 'SchoolId'].unique()
        all_departments = co_df['DepartmentName', 'DepartmentId'].unique()
        all_programs = co_df['ProgramName', 'ProgramId'].unique()

        # po_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 7"
        # po_df = db_connector.execute_query(po_query)
        # po_df = po_df.rename(columns={'Attainmemt': 'Attainment'})
        # if po_df is None:
        #     raise Exception("Failed to load PO data from database.")
    except Exception as e:
        print(f"Error during database operations: {e}")


# --- START - Helper Functions ---
# --------------------------------
def execute_sql_query(query, db_name):

    db_connector = DatabaseConnector(DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD)
    try:
        db_connector.connect()
        # --- Data Loading and Preprocessing ---
        # co_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 2"
        result = db_connector.execute_query(query)
        result = result.rename(columns={'Attainmemt': 'Attainment'})
        if result is None:
            raise Exception("Failed to load CO data from database.")
        else:
            return result

    except Exception as e:
        print(f"Error during database operations: {e}")


def get_AI_client():
    endpoint = "https://ai-inpodsoutcomesanalysis01667696755442.openai.azure.com/"
    model_name = "gpt-35-turbo-16k"
    deployment = "gpt-35-turbo-16k"

    subscription_key = "6hLxnG751HGjuvx8iH2ZKZJRVIwvvP4rIwCvUHAkDeLMk5eLZkHFJQQJ99BCACHYHv6XJ3w3AAAAACOG8Xbv"
    api_version = "2024-12-01-preview"

    client = AzureOpenAI(
        api_version=api_version,
        azure_endpoint=endpoint,
        api_key=subscription_key,
    )
    return client


def get_outcomes_from_csv(filename):
    dtypes = {
        "Attainment": 'float',
        "Target": 'float',
        "Threshold": 'float'
    }
    outcomes_df = pd.read_csv(filename, encoding='utf8', low_memory=False, skipinitialspace=True, dtype=dtypes,
                              keep_default_na=True)
    values = {"Threshold": 0, "CourseId": 0}
    outcomes_df.fillna(values)
    outcomes_df['CourseId'] = 0
    return (outcomes_df)


def filter_outcomes_data(df, level):
    filtered_df = df[df['COAttainmentLevel'] == level]
    return filtered_df


def get_copo_attainment(df):
    metadata_df = df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'BatchId', 'BatchName',
         'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'COId', 'COName', 'CODesc', 'POId', 'POName',
         'PODesc']].drop_duplicates()
    copo_df = df[['COId', 'COName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target',
                  'Threshold']].drop_duplicates()
    copo_attain_df = copo_df.groupby(['COId', 'COName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
    merged_df = pd.merge(copo_attain_df, metadata_df, how='inner', left_on=['COId', 'COName', 'POId', 'POName'],
                         right_on=['COId', 'COName', 'POId', 'POName'])
    return merged_df


def get_schoolwise_attainment(merged_df):
    schoolwise_attainment = merged_df[
        ['SchoolId', 'SchoolName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['SchoolId', 'SchoolName']).mean(numeric_only=True).reset_index()
    return schoolwise_attainment


def get_schoolwise_attainment_chart(schoolwise_attainment):
    # Create a copy to avoid modifying the original dataframe
    df = schoolwise_attainment.copy()
    
    # Add trimmed school names for display - showing first 25 characters
    df['TrimmedSchoolName'] = df['SchoolName'].apply(
        lambda x: f"{x[:25]}..." if len(x) > 25 else x
    )
    
    # Create the figure with trimmed names and proper labels
    fig = px.bar(
        df, 
        x="TrimmedSchoolName", 
        y=["Attained", "NotAttained"],
        labels={
            "TrimmedSchoolName": "School Name", 
            "value": "CO Count", 
            "variable": "Attainment Status"
        },
        title="School-wise CO Attainment Count"
    )
    
    # Update hover template to show full school name
    for i, trace in enumerate(fig.data):
        measure_name = "Attained" if i == 0 else "Not Attained"
        fig.data[i].hovertemplate = '<b>School:</b> %{customdata}<br>' + \
                                    f'<b>{measure_name} Count:</b> %{{y}}<extra></extra>'
        fig.data[i].customdata = df['SchoolName']
        
        # Explicitly update trace names to ensure they appear in the legend
        if i == 0:
            fig.data[i].name = "Attained"
        else:
            fig.data[i].name = "Not Attained"
    
    # Set x-axis tick angle to -45 degrees and update layout
    fig.update_layout(
        xaxis_tickangle=-45,
        xaxis_title="School Name",
        yaxis_title="CO Attainment Count",
        legend_title="Legend",
        barmode='stack'  # Changed from 'group' to 'stack'
    )
    
    return fig


def get_program_wise_attainment(copo_attainment_df):
    pgm_wise_attain_df = copo_attainment_df[
        ['ProgramId', 'ProgramName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['ProgramId', 'ProgramName']).mean(numeric_only=True).reset_index()
    metadata_df = copo_attainment_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName']].drop_duplicates()
    merged_df = pd.merge(pgm_wise_attain_df, metadata_df, how='inner', left_on=['ProgramId', 'ProgramName'],
                         right_on=['ProgramId', 'ProgramName'])
    return merged_df


def get_programwise_attainment_chart(pgmwise_attainment):
    fig = px.bar(pgmwise_attainment, x="ProgramName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', ],
                 barmode='group', template='seaborn')
    # Add target markers
    # fig.update_layout(xaxis={'categoryorder':'total descending'})
    fig.update_layout(height=600)
    return fig


def get_program_batch_wise_attainment(merged_df):
    program_batch_wise_attainment = merged_df[
        ['ProgramId', 'ProgramName', 'BatchId', 'BatchName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL',
         'Target']].groupby(['ProgramId', 'ProgramName', 'BatchId', 'BatchName']).mean(numeric_only=True).reset_index()
    return program_batch_wise_attainment


def get_program_batch_wise_attainment_chart(program_batch_wise_attainment):
    fig = go.Figure()
    program_batch_wise_attainment = program_batch_wise_attainment.sort_values(by=['ProgramName', 'BatchName'],
                                                                              ascending=[True, True])
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainment across Program Batches ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category descending'})
    return fig


def get_program_po_wise_attainment(merged_df):
    pgm_po_wise_attain_df = merged_df[
        ['ProgramId', 'ProgramName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL',
         'Target']].groupby(['ProgramId', 'ProgramName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
    metadata_df = merged_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'POId', 'POName',
         'PODesc', 'SemesterId', 'SemesterName']].drop_duplicates()
    merged_df = pd.merge(pgm_po_wise_attain_df, metadata_df, how='inner',
                         left_on=['ProgramId', 'ProgramName', 'POId', 'POName'],
                         right_on=['ProgramId', 'ProgramName', 'POId', 'POName'])
    return merged_df


def get_program_po_wise_attainment_chart(pgm_po_wise_attain_df):
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainments across Program level Program Objectives ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category ascending'})
    return fig


def get_powise_attainment(merged_df):
    powise_po_attain_df = merged_df[
        ['POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['POId', 'POName']).mean(numeric_only=True).reset_index()
    metadata_df = merged_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'POId', 'POName',
         'PODesc']].drop_duplicates()
    merged_df = pd.merge(powise_po_attain_df, metadata_df, how='inner', left_on=['POId', 'POName'],
                         right_on=['POId', 'POName'])
    return merged_df


def get_powise_attainment_chart(powise_po_attain_df):
    fig = px.bar(powise_po_attain_df, x="POName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL'],
                 barmode='group')
    # Add target markers
    fig.add_trace(
        go.Scatter(x=powise_po_attain_df['POName'], y=powise_po_attain_df['Target'], mode='markers', name='Target'))
    fig.update_layout(xaxis={'categoryorder': 'total descending'})
    fig.update_layout(height=600)
    return fig


# def analyze_using_ai(str):
#     response = client.chat.completions.create(
#         messages=[
#             {
#                 "role": "system",
#                 "content": "You are a helpful assistant.",
#             },
#             {
#                 "role": "user",
#                 "content": "The following shows the programs at the university that had the lowest attainment in the specified program outcomes. Use the descriptions and meanings of lowest performing program outcomes and make specific recommendations for each program with recommendations to improve the outcomes. Cover all programs and for each listed program, group recommended actions. :" + str,
#             }
#         ],
#         max_tokens=4096,
#         temperature=1.0,
#         top_p=1.0,
#         model="gpt-35-turbo-16k"
#     )
#     return response.choices[0].message.content


# def get_course_po_wise_attainment(df):
#     coursewise_po_attain_df = df[['CourseId', 'CourseName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(['CourseId', 'CourseName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
#     metadata_df =  df[['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'BatchId', 'BatchName', 'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'POId', 'POName', 'PODesc' ]].drop_duplicates()
#     merged_df = pd.merge(coursewise_po_attain_df, metadata_df, how='inner', left_on=['CourseId','CourseName', 'POId', 'POName'], right_on=['CourseId', 'CourseName', 'POId', 'POName'])
#     return merged_df


# def get_coursewise_po_attainment_chart_old(coursewise_po_attainment):
#     fig = px.bar(coursewise_po_attainment, x="CourseName", y=['POAttDWOAL'], barmode='stack')
#     # Add target markers
#     fig.update_layout(xaxis={'categoryorder':'total descending'})
#     fig.update_layout( height=900)
#     return fig


def get_coursewise_po_attainment_chart(coursewise_po_attainment, failingOnly):
    if (failingOnly == True):
        fig = px.bar(
            coursewise_po_attainment[coursewise_po_attainment['POAttDWOAL'] <= coursewise_po_attainment['Target']],
            x="CourseName", y=['POAttDWOAL'], barmode='stack')
    else:
        fig = px.bar(coursewise_po_attainment, x="CourseName", y=['POAttDWOAL'], barmode='stack')
    # Add target markers
    fig.update_layout(xaxis={'categoryorder': 'total descending'})
    fig.update_layout(height=900)
    return fig


def get_coursewise_po_attainment(df):
    coursewise_po_attain_df = df[
        ['CourseId', 'CourseName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['CourseId', 'CourseName']).mean(numeric_only=True).reset_index()
    metadata_df = df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'BatchId', 'BatchName',
         'SemesterId', 'SemesterName', 'CourseId', 'CourseName']].drop_duplicates()
    merged_df = pd.merge(coursewise_po_attain_df, metadata_df, how='inner', left_on=['CourseId', 'CourseName'],
                         right_on=['CourseId', 'CourseName'])
    return merged_df


def build_level2_df(df):
    temp_df = df
    level2_df = temp_df.groupby(['SchoolId', 'SchoolName', 'CourseId', 'CourseName', 'COName']).mean(
        numeric_only=True).reset_index()
    level2_df['Attained'] = level2_df['Attainment'] >= level2_df['Threshold']
    level2_df['NotAttained'] = level2_df['Attainment'] < level2_df['Threshold']
    level2_df = level2_df[['SchoolId', 'SchoolName', 'Attained', 'NotAttained']].groupby(
        ['SchoolId', 'SchoolName']).sum().reset_index()
    return level2_df


def get_schoolwise_attainment_100chart(level2_df):
    # Create a copy to avoid modifying the original dataframe
    df = level2_df.copy()
    
    # Calculate percentages
    df['Attained_pct'] = df['Attained'] / (df['Attained'] + df['NotAttained']) * 100
    df['NotAttained_pct'] = df['NotAttained'] / (df['Attained'] + df['NotAttained']) * 100
    
    # Add trimmed school names for display - showing first 25 characters
    df['TrimmedSchoolName'] = df['SchoolName'].apply(
        lambda x: f"{x[:25]}..." if len(x) > 25 else x
    )
    
    # Create the figure with trimmed names and proper labels
    fig = px.bar(
        df, 
        x="TrimmedSchoolName", 
        y=["Attained_pct", "NotAttained_pct"],
        labels={
            "TrimmedSchoolName": "School Name", 
            "value": "Percentage (%)", 
            "variable": "Attainment Status"
        },
        title="School-wise CO Attainment Percentage"
    )
    
    # Update hover template to show full school name
    for i, trace in enumerate(fig.data):
        measure_name = "Attained" if i == 0 else "Not Attained"
        fig.data[i].hovertemplate = '<b>School:</b> %{customdata}<br>' + \
                                    f'<b>{measure_name}:</b> %{{y:.1f}}%<extra></extra>'
        fig.data[i].customdata = df['SchoolName']
        
        # Explicitly update trace names to ensure they appear in the legend
        if i == 0:
            fig.data[i].name = "Attained"
        else:
            fig.data[i].name = "Not Attained"
    
    # Set x-axis tick angle to -45 degrees and update layout
    fig.update_layout(
        xaxis_tickangle=-45,
        xaxis_title="School Name",
        yaxis_title="CO Attainment Percentage (%)",
        legend_title="Legend",
        barmode='stack'  # Changed from 'group' to 'stack'
    )
    
    return fig


def get_schoolwise_attainment_distr(df):
    filtered_df = df[df['COAttainmentLevel'] == 3][
        ['SchoolId', 'SchoolName', 'CourseId', 'CourseName', 'COName', 'Attainment']].drop_duplicates()
    avg_attainment_df = filtered_df.groupby(['SchoolId', 'SchoolName', 'CourseId', 'CourseName']).mean(
        numeric_only=True).reset_index()
    # Define bins and labels
    print(avg_attainment_df)
    bins = [0, 20, 40, 60, 80, 100]
    labels = ['0-20', '21-40', '41-60', '61-80', '81-100']
    # Create buckets using pd.cut()
    avg_attainment_df['bucket'] = pd.cut(avg_attainment_df['Attainment'], bins=bins, labels=labels, right=False)
    attainment_buckets_df = avg_attainment_df[['SchoolId', 'SchoolName', 'bucket']]
    attainment_buckets_df['count'] = 1
    schoolwise_buckets_distr_df = attainment_buckets_df.groupby(['SchoolId', 'SchoolName', 'bucket'],
                                                                observed=True).count().reset_index()
    schoolwise_buckets_distr_df = schoolwise_buckets_distr_df.pivot_table('count', ['SchoolName'], 'bucket').fillna(0)
    return schoolwise_buckets_distr_df


# Add a new function to get the top N unattained COs
def get_top_unattained_cos(filtered_df, limit=20):
    """
    Get the top N COs that are not attained, ordered by attainment percentage in ascending order.
    A CO is considered unattained if its attainment percentage is less than its threshold.
    
    Args:
        filtered_df: DataFrame with CO attainment data
        limit: Number of top unattained COs to return (default: 50)
    
    Returns:
        DataFrame with top N unattained COs
    """
    # Filter for section-level data (level 2)
    section_level_df = filtered_df[filtered_df['COAttainmentLevel'] == 3].copy()
    
    # Calculate if CO is attained (Attainment >= Threshold)
    section_level_df['IsAttained'] = section_level_df['Attainment'] >= section_level_df['Threshold']
    
    # Group by CO and Course to get attainment status
    co_attainment = section_level_df.groupby(['COName', 'CODesc', 'CourseId', 'CourseName']).agg(
        Attainment=('Attainment', 'mean'),
        Threshold=('Threshold', 'mean'),
        IsAttained=('IsAttained', 'mean')  # This gives percentage of sections where CO is attained
    ).reset_index()
    
    # Mark COs as unattained if attainment is less than threshold
    co_attainment['IsUnattained'] = co_attainment['Attainment'] < co_attainment['Threshold']
    
    # Filter for COs that are not attained
    unattained_cos = co_attainment[co_attainment['IsUnattained']]
    
    # Sort by attainment percentage (ascending to get the worst performing first)
    unattained_cos = unattained_cos.sort_values('Attainment', ascending=True)
    
    # Take top N
    top_unattained = unattained_cos.head(limit)
    
    return top_unattained

# Add a function to create the chart for unattained COs
def get_unattained_cos_chart(top_unattained_cos):
    """
    Create a bar chart showing the top unattained COs.
    
    Args:
        top_unattained_cos: DataFrame with top unattained COs
    
    Returns:
        Plotly figure object
    """
    if top_unattained_cos.empty:
        return go.Figure()
    
    # Create a custom label for x-axis that includes CO name
    top_unattained_cos['COLabel'] = top_unattained_cos['COName'].apply(
        lambda x: f"{x[:25]}..." if len(x) > 25 else x
    )
    
    # Calculate the gap between threshold and attainment
    top_unattained_cos['Gap'] = top_unattained_cos['Threshold'] - top_unattained_cos['Attainment']
    
    # Create the figure
    fig = go.Figure()
    
    # Add bars for attainment
    fig.add_trace(go.Bar(
        x=top_unattained_cos['COLabel'],
        y=top_unattained_cos['Attainment'],
        name='Attainment',
        marker_color=colors[0],
        customdata=top_unattained_cos[['CourseName', 'CODesc', 'Gap']],
        hovertemplate='<b>CO:</b> %{x}<br>' +
                      '<b>Course:</b> %{customdata[0]}<br>' +
                      '<b>Description:</b> %{customdata[1]}<br>' +
                      '<b>Attainment:</b> %{y:.1f}%<br>' +
                      '<b>Gap to Threshold:</b> %{customdata[2]:.1f}%<extra></extra>'
    ))
    
    # Add threshold markers
    fig.add_trace(go.Scatter(
        x=top_unattained_cos['COLabel'],
        y=top_unattained_cos['Threshold'],
        mode='markers',
        name='Threshold',
        marker=dict(
            color='#E05D5D',
            symbol='diamond',
            size=8
        ),
        customdata=top_unattained_cos[['CourseName', 'CODesc']],
        hovertemplate='<b>CO:</b> %{x}<br>' +
                      '<b>Course:</b> %{customdata[0]}<br>' +
                      '<b>Threshold:</b> %{y:.1f}%<extra></extra>'
    ))
    
    # Update layout
    fig.update_layout(
        title='',
        xaxis=dict(
            title='Course Outcome',
            tickangle=-45,
            tickmode='array',
            tickvals=top_unattained_cos['COLabel'],
            ticktext=top_unattained_cos['COLabel']
        ),
        yaxis=dict(
            title='Attainment Percentage',
            range=[0, 100]
        ),
        legend_title='Legend',
        height=600,
        margin=dict(t=30, b=150)  # Add bottom margin for rotated labels
    )
    
    return fig


def get_programwise_attainment(univ_df):
    # Filter for section-wise data (COAttainmentLevel == 3)
    sectionwise_df = univ_df[univ_df['COAttainmentLevel'] == 3]
    
    # Get unique program-CO combinations with attainment data
    pgm_co_df = sectionwise_df[
        ['ProgramId', 'ProgramName', 'COId', 'COName', 'Attainment', 'Threshold']
    ].drop_duplicates()
    
    # Group by program and CO to aggregate attainment data
    pgm_co_attn_df = pgm_co_df.groupby(['ProgramId', 'ProgramName', 'COId', 'COName']).agg({
        'Attainment': 'sum',
        'Threshold': 'first'  # Assuming threshold is consistent for same CO
    }).reset_index()
    
    # Calculate attained/not attained flags
    pgm_co_attn_df['Attained'] = (pgm_co_attn_df['Attainment'] >= pgm_co_attn_df['Threshold']).astype(int)
    pgm_co_attn_df['NotAttained'] = (pgm_co_attn_df['Attainment'] < pgm_co_attn_df['Threshold']).astype(int)
    
    # Aggregate by program to get final counts
    result_df = pgm_co_attn_df.groupby(['ProgramId', 'ProgramName']).agg({
        'Attained': 'sum',
        'NotAttained': 'sum'
    }).reset_index()
    
    # Calculate percentages
    total_cos = result_df['Attained'] + result_df['NotAttained']
    result_df['AttainedPct'] = (result_df['Attained'] * 100.0 / total_cos).round(2)
    result_df['NotAttainedPct'] = (result_df['NotAttained'] * 100.0 / total_cos).round(2)
    
    # Return only the required fields
    final_result = result_df[['ProgramName', 'Attained', 'NotAttained', 'AttainedPct', 'NotAttainedPct']]
    
    print(final_result)
    return final_result


def get_programwise_attainment_chart(pgmwise_attainment_df):
    # Create a copy to avoid modifying the original dataframe
    df = pgmwise_attainment_df.copy()

    # Add trimmed program names for display - showing first 25 characters
    df['TrimmedProgramName'] = df['ProgramName'].apply(
        lambda x: f"{x[:25]}..." if len(x) > 25 else x
    )

    # Create unique identifiers to prevent bars from being combined
    # when trimmed names are the same
    trimmed_counts = df['TrimmedProgramName'].value_counts()
    df['UniqueDisplayName'] = df['TrimmedProgramName'].copy()

    # Add suffix to duplicated trimmed names to make them unique
    for trimmed_name in trimmed_counts[trimmed_counts > 1].index:
        mask = df['TrimmedProgramName'] == trimmed_name
        duplicate_rows = df[mask]
        for i, (idx, row) in enumerate(duplicate_rows.iterrows()):
            df.loc[idx, 'UniqueDisplayName'] = f"{trimmed_name} ({i+1})"

    # Create the figure with custom names for the traces
    fig = px.bar(
        df,
        x="UniqueDisplayName",
        y=["Attained", "NotAttained"],
        labels={"Attained": "Attained", "NotAttained": "Not Attained"},
        title="Program-wise CO Attainment Count"
    )

    # Update hover template to show full program name
    for i, trace in enumerate(fig.data):
        measure_name = "Attained" if i == 0 else "Not Attained"
        fig.data[i].hovertemplate = '<b>Program:</b> %{customdata}<br>' + \
                                    f'<b>{measure_name} Count:</b> %{{y}}<extra></extra>'
        fig.data[i].customdata = df['ProgramName']

    # Update layout to use the unique display names on x-axis
    fig.update_layout(
        xaxis_title="Program Name",
        yaxis_title="CO Attainment Count",
        xaxis_tickangle=-45,
        legend_title="Legend"
    )

    return fig


def get_programwise_attainment_pct_chart(pgmwise_attainment_df):
    # Create a copy to avoid modifying the original dataframe
    df = pgmwise_attainment_df.copy()

    # Add trimmed program names for display - showing first 25 characters
    df['TrimmedProgramName'] = df['ProgramName'].apply(
        lambda x: f"{x[:25]}..." if len(x) > 25 else x
    )

    # Create unique identifiers to prevent bars from being combined
    # when trimmed names are the same
    trimmed_counts = df['TrimmedProgramName'].value_counts()
    df['UniqueDisplayName'] = df['TrimmedProgramName'].copy()

    # Add suffix to duplicated trimmed names to make them unique
    for trimmed_name in trimmed_counts[trimmed_counts > 1].index:
        mask = df['TrimmedProgramName'] == trimmed_name
        duplicate_rows = df[mask]
        for i, (idx, row) in enumerate(duplicate_rows.iterrows()):
            df.loc[idx, 'UniqueDisplayName'] = f"{trimmed_name} ({i+1})"

    # Create the figure with custom names for the traces
    fig = px.bar(
        df,
        x="UniqueDisplayName",
        y=["AttainedPct", "NotAttainedPct"],
        labels={"AttainedPct": "Attained", "NotAttainedPct": "Not Attained"},
        title="Program-wise CO Attainment Percentage"
    )

    # Update hover template to show full program name
    for i, trace in enumerate(fig.data):
        measure_name = "Attained" if i == 0 else "Not Attained"
        fig.data[i].hovertemplate = '<b>Program:</b> %{customdata}<br>' + \
                                    f'<b>{measure_name}:</b> %{{y:.1f}}%<extra></extra>'
        fig.data[i].customdata = df['ProgramName']

        # Explicitly update trace names to ensure they appear in the legend
        if i == 0:
            fig.data[i].name = "Attained"
        else:
            fig.data[i].name = "Not Attained"

    # Update layout to use the unique display names on x-axis
    fig.update_layout(
        xaxis_title="Program Name",
        yaxis_title="CO Attainment Percentage",
        xaxis_tickangle=-45,
        legend_title="Legend"
    )

    return fig


def get_lowest_performing_courses(filtered_df, limit=20):
    """
    Calculate average CO attainment at course level and return the lowest performing courses.
    
    Args:
        filtered_df: DataFrame with CO attainment data
        limit: Number of lowest performing courses to return (default: 20)
    
    Returns:
        DataFrame with the lowest performing courses
    """
    # Filter for course-level data (level 2)
    course_level_df = filtered_df[filtered_df['COAttainmentLevel'] == 3].copy()
    
    # Group by Course to get average attainment
    course_attainment = course_level_df.groupby(['CourseId', 'CourseName']).agg(
        Attainment=('Attainment', 'mean'),
        Threshold=('Threshold', 'mean')
    ).reset_index()
    
    # Calculate gap between threshold and attainment
    course_attainment['Gap'] = course_attainment['Threshold'] - course_attainment['Attainment']
    
    # Sort by attainment (ascending to get the worst performing first)
    sorted_courses = course_attainment.sort_values('Attainment', ascending=True)
    
    # Take top N lowest performing
    lowest_performing = sorted_courses.head(limit)
    
    return lowest_performing


def get_lowest_performing_courses_chart(lowest_courses_df):
    """
    Create a bar chart showing the N lowest performing courses by CO attainment.
    """
    # Create a copy to avoid modifying the original dataframe
    df = lowest_courses_df.copy()
    
    # Add trimmed course names for display - showing last 25 characters
    df['TrimmedCourseName'] = df['CourseName'].apply(
        lambda x: f"...{x[-25:]}" if len(x) > 25 else x
    )
    
    # Create the figure
    fig = go.Figure()
    
    # Add bars for attainment
    fig.add_trace(go.Bar(
        x=df['TrimmedCourseName'],
        y=df['Attainment'],
        name='Attainment',
        marker_color=colors[0],
        customdata=df[['CourseName', 'Gap', 'Threshold']],
        hovertemplate='<b>Course:</b> %{customdata[0]}<br>' +
                      '<b>Attainment:</b> %{y:.1f}%<br>' +
                      '<b>Threshold:</b> %{customdata[2]:.1f}%<br>' +
                      '<b>Gap:</b> %{customdata[1]:.1f}%<extra></extra>'
    ))
    
    # Add threshold markers
    fig.add_trace(go.Scatter(
        x=df['TrimmedCourseName'],
        y=df['Threshold'],
        mode='markers',
        name='Threshold',
        marker=dict(color='#E05D5D', size=8, symbol='diamond'),
        customdata=df['CourseName'],
        hovertemplate='<b>Course:</b> %{customdata}<br>' +
                      '<b>Threshold:</b> %{y:.1f}%<extra></extra>'
    ))
    
    # Update layout
    fig.update_layout(
        title='',
        xaxis_title='Course',
        yaxis_title='Attainment (%)',
        yaxis_range=[0, 100],
        xaxis_tickangle=-45,
        height=600,
        margin=dict(t=30, b=150)  # Reduce top margin since there's no title
    )
    
    return fig


def get_filtered_dataset(dataset, master_school_id, selected_schools, selected_departments,
                         selected_programs):
    filtered_df = dataset[dataset['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    if selected_departments:
        filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]
    if selected_programs:
        filtered_df = filtered_df[filtered_df['ProgramName'].isin(selected_programs)]
    return filtered_df


def get_filtered_courses(dataset, master_school_id, selected_schools, selected_departments,
                         selected_programs):
    filtered_df = get_filtered_dataset(dataset, master_school_id, selected_schools, selected_departments,
                                       selected_programs)
    return filtered_df[['CourseName', 'CourseId']].drop_duplicates()


# --- END - Helper Functions ---
# --------------------------------


# outcomes_df = get_outcomes_from_csv("gu-podata.csv")
# outcomes_df = get_outcomes_from_csv("pages/CIMR_data - PO_data.csv")
# outcomes_df = filter_outcomes_data(outcomes_df, 7)
# copo_attainment_df = get_copo_attainment(outcomes_df)
# # schoolwise_attainment = get_schoolwise_attainment(copo_attainment_df)
# # schoolwise_attainment_fig = get_schoolwise_attainment_chart(schoolwise_attainment)
# pgmwise_attainment = get_program_wise_attainment(copo_attainment_df)
# # pgmwise_attainment_fig = get_programwise_attainment_chart(pgmwise_attainment)
# program_batch_wise_attainment = get_program_batch_wise_attainment(copo_attainment_df)
# program_batch_wise_attainment_fig = get_program_batch_wise_attainment_chart(program_batch_wise_attainment)
# pgm_po_wise_attain_df = get_program_po_wise_attainment(copo_attainment_df)
# pgmpo_wise_attainment_fig = get_program_po_wise_attainment_chart(pgmwise_attainment)
# powise_po_attain_df = get_powise_attainment(copo_attainment_df)
# powise_attain_fig = get_powise_attainment_chart(powise_po_attain_df)
# n_smallest_rows = powise_po_attain_df.nsmallest(20, 'POAttDWOAL')
# n_smallest_rows[['ProgramName', 'POName', 'PODesc', 'POAttDWOAL']].rename(inplace=True,
#                                                                           columns={'ProgramName': 'Program Name',
#                                                                                    'POName': 'Program Outcome',
#                                                                                    'PODesc': 'Program Outcome Description',
#                                                                                    'POAttDWOAL': 'Attainment'})
# json_string = n_smallest_rows.to_json(orient='records', lines=True)

# client = get_AI_client()
# analysis = analyze_using_ai(json_string)
# print(analysis)

# course_po_wise_attain_df = get_course_po_wise_attainment(copo_attainment_df)
# print(course_po_wise_attain_df)
# coursewise_po_attainment = get_coursewise_po_attainment(copo_attainment_df)
# print(coursewise_po_attainment.dtypes)
# print(coursewise_po_attainment)
# course_po_attainment_fig_all = get_coursewise_po_attainment_chart(coursewise_po_attainment, False)
# course_po_attainment_fig_failing = get_coursewise_po_attainment_chart(coursewise_po_attainment, True)

# CO related code
# --- Data Preprocessing ---


courses_df = co_df[['CourseName', 'CourseId']].drop_duplicates()
# Convert DataFrame columns into dropdown options
course_options = [
    {'label': row['CourseName'], 'value': row['CourseId']} for _, row in courses_df.iterrows()
]

# Dimension columns
dim_sectionId = 'SectionId'
dim_coname = 'COName'
dim_assgn = 'AssignmentName'
# Measure column
measure_col = 'Attainment'

# Updated color palette with more visually appealing colors
colors = [
    '#5C88DA',  # Muted Blue
    '#7DBF70',  # Soft Green
    '#F2A65A',  # Warm Orange
    '#9B7ECF',  # Lavender/Purple
    '#E05D5D',  # Dusty Red
    '#66B0D5',  # Sky Blue
    '#D9B38F',  # Muted Brown
    '#7DCEF0',  # Lighter Blue
    '#B0D98F',  # Pale Green
    '#FFD28C'   # Light Peach
]

# Get unique values for the second dimension (for creating traces)
subcat_co = co_df[dim_coname].unique()
subcat_assgn = co_df[dim_assgn].unique()

# Define Sidebar Widths
SIDEBAR_WIDTH_EXPANDED = "280px" # Use a fixed pixel width for better control
SIDEBAR_WIDTH_COLLAPSED = "60px" # Just enough for the icon
NAVBAR_HEIGHT = "52px"

# --- Layout Definition ---
# --- Layout Definition ---
layout = html.Div([
    html.Div([
        # --- Left Sidebar ---
        html.Div([
            # Sidebar Header (Toggle Button + "Filters" text)
            html.Div([
                html.Button(
                    html.I(className="fas fa-bars"),
                    id='sidebar-toggle-button',
                    n_clicks=0,
                    style={
                        'background': 'none',
                        'border': 'none',
                        'color': '#495057', # Darker text/icon color for contrast
                        'fontSize': '24px',
                        'cursor': 'pointer',
                        'padding': '15px',
                        'position': 'absolute',
                        'right': '0px',
                        'top': '0px',
                        'z-index': '1'
                    }
                ),
                html.H3("Filters", id='filters-title', style={
                    'color': '#495057', # Darker text color
                    'margin': '0',
                    'padding': '15px 15px 15px 20px',
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis',
                    'transition': 'opacity 0.3s ease-in-out',
                    'opacity': 1
                })
            ], style={
                'background-color': '#e9ecef', # Lighter grey for header
                'padding': '0',
                'height': '60px',
                'display': 'flex',
                'align-items': 'center',
                'position': 'relative'
            }),

            # Collapsible content (filters)
            html.Div([
                html.H4("Select Schools", style={'color': '#495057'}), # Darker text for labels
                dcc.Dropdown(
                    id='school-filter-dropdown-co',
                    options=[],
                    value=[],
                    placeholder="Select Schools",
                    multi=True,
                    style={'marginBottom': '15px'}
                ),
                html.H4("Select Departments", style={'color': '#495057'}),
                dcc.Dropdown(
                    id='department-filter-dropdown-co',
                    options=[],
                    value=[],
                    placeholder="Select Departments",
                    multi=True,
                    style={'marginBottom': '15px'}
                ),
                html.H4("Select Programs", style={'color': '#495057'}),
                dcc.Dropdown(
                    id='program-filter-dropdown-co',
                    options=[],
                    value=[],
                    placeholder="Select Programs",
                    multi=True,
                    style={'marginBottom': '15px'}
                )
            ], id='sidebar-content', style={
                'padding': '20px',
                'flex-grow': 1,
                'overflow-y': 'auto',
                'opacity': 1,
                'transition': 'opacity 0.3s ease-in-out',
                'background-color': '#f8f9fa' # Very light grey for the content background
            })
        ], id='sidebar', style={
            'position': 'fixed',
            'top': NAVBAR_HEIGHT,
            'left': '0',
            'bottom': '0',
            'width': SIDEBAR_WIDTH_EXPANDED,
            'background-color': '#f8f9fa', # Main sidebar body background (lightest grey)
            'box-shadow': '2px 0 5px rgba(0,0,0,.1)',
            'z-index': '1050',
            'transition': 'width 0.3s ease-in-out',
            'display': 'flex',
            'flex-direction': 'column',
        }),

        # --- Main Content Area ---
        html.Div([
            html.H2("Course Outcomes Attainment Analysis", style={'margin-bottom': '20px'}), # Moved this H2 here
            html.H3("School-wise Attainment"),
            html.Div(
                children=[
                    html.Div(dcc.Graph(id='school-wise-attainment-chart'), style={'width': '48%', 'display': 'inline-block'}),
                    html.Div(dcc.Graph(id='school-wise-attainment-pct-chart'), style={'width': '48%', 'display': 'inline-block'}),
                ],
                style={'width': '100%', 'display': 'inline-block'}
            ),
            html.H3("Program-wise CO Attainment"),
            html.Div(
                children=[
                    html.Div(dcc.Graph(id='pgmwise-attainment-chart'), style={'width': '48%', 'display': 'inline-block'}),
                    html.Div(dcc.Graph(id='pgmwise-attainment-pct-chart'), style={'width': '48%', 'display': 'inline-block'}),
                ],
                style={'width': '100%', 'display': 'inline-block'}
            ),
            html.Div([
                html.H3("Lowest Performing Courses", className="chart-title"),
                html.Div([
                    html.Label("Show: ", style={'display': 'inline-block', 'vertical-align': 'middle'}),
                    dcc.Dropdown(
                        id='lowest-courses-limit',
                        options=[
                            {'label': '5', 'value': 5},
                            {'label': '10', 'value': 10},
                            {'label': '20', 'value': 20},
                            {'label': '50', 'value': 50}
                        ],
                        value=20,  # Default to showing top 20
                        clearable=False,
                        style={'width': '100px', 'display': 'inline-block', 'vertical-align': 'middle'}
                    )
                ]),
                dcc.Graph(
                    id='lowest-performing-courses-chart',
                    figure={},
                    config={'displayModeBar': True}
                ),
            ], className="chart-container"),
            html.H3("Lowest Performing Course Outcomes"),
            html.Div([
                html.Label("Show: ", style={'display': 'inline-block', 'vertical-align': 'middle'}),
                dcc.Dropdown(
                    id='unattained-cos-limit',
                    options=[
                        {'label': '5', 'value': 5},
                        {'label': '10', 'value': 10},
                        {'label': '20', 'value': 20},
                        {'label': '50', 'value': 50}
                    ],
                    value=20,  # Default to showing top 20
                    clearable=False,
                    style={'width': '100px', 'display': 'inline-block', 'vertical-align': 'middle'}
                )
            ]),
            dcc.Graph(id='unattained-cos-chart'),
            html.H3("Average Course Attainment Percentage"),
            html.Div([
                dash_table.DataTable(
                    id='schoolwise-co-attainment-table',
                    page_size=10,
                    style_as_list_view=True,
                    style_data_conditional=[
                        { 'if': {'row_index': 'odd'}, 'backgroundColor': '#f9f9f9' },
                        { 'if': { 'filter_query': '{Attainment Range (%)} = "81-100"', 'column_id': 'Attainment Range (%)' },
                          'backgroundColor': '#d4edda', 'color': '#155724' },
                        { 'if': { 'filter_query': '{Attainment Range (%)} = "0-20"', 'column_id': 'Attainment Range (%)' },
                          'backgroundColor': '#f8d7da', 'color': '#721c24' }
                    ]
                )
            ], style={'margin-bottom': '60px'}),

            # Add a wrapper div for the selectors that will become sticky
            html.Div([
                html.H3("Course Details:", style={'margin-right': '15px', 'vertical-align': 'middle', 'margin-bottom': '10px', 'margin-top': '0px'}),
                html.Div([
                    html.Div([
                        html.Label("Course:", style={'display': 'block', 'margin-bottom': '5px', 'font-weight': 'bold'}),
                        dcc.Dropdown(
                            id='course-selector',
                            options=course_options,
                            value=None,
                            placeholder="Select Course",
                            style={
                                'width': '100%'
                            },
                            clearable=True,
                            optionHeight=60,
                            maxHeight=400
                        )
                    ], style={'flex': '2', 'margin-right': '10px', 'min-width': '300px'}),
                    
                    html.Div([
                        html.Label("Section:", style={'display': 'block', 'margin-bottom': '5px', 'font-weight': 'bold'}),
                        dcc.Dropdown(
                            id='section-selector',
                            options=[],
                            value=None,
                            placeholder="Select Section",
                            style={
                                'width': '100%'
                            },
                            clearable=True,
                            optionHeight=60,
                            maxHeight=400
                        )
                    ], style={'flex': '2', 'margin-right': '10px', 'min-width': '300px'}),
                    
                    html.Div([
                        html.Label("Course Outcome:", style={'display': 'block', 'margin-bottom': '5px', 'font-weight': 'bold'}),
                        dcc.Dropdown(
                            id='co-selector',
                            options=[],
                            value=None,
                            placeholder="Select CO",
                            style={
                                'width': '100%'
                            },
                            clearable=True,
                            optionHeight=60,
                            maxHeight=400
                        )
                    ], style={'flex': '1', 'min-width': '200px'})
                ], style={
                    'display': 'flex', 
                    'flex-direction': 'row', 
                    'align-items': 'flex-end',
                    'flex-wrap': 'wrap',  # Allow wrapping on smaller screens
                    'width': '100%'
                }),
            ], id='sticky-selectors', style={
                'position': 'sticky',
                'top': '-12px',  # Stick below the navbar
                'background-color': '#ffffff',
                'z-index': '1000',
                'width': '100%',
                'padding': '8px 8px',
                'margin-bottom': '20px',
                'box-shadow': '0 2px 5px rgba(0,0,0,0.1)'
            }),
            
            # Charts and other content that will scroll
            html.Div([
                dcc.Graph(id='course-bar-chart'),
                dcc.Graph(id='course-assgn-bar-chart'),
                html.Div(dcc.Graph(id='sect-student-co-attainment-chart'), style={'margin-bottom': '20px'}),
            ], id='scrollable-content'),
        ], id='main-content', style={
            'margin-left': SIDEBAR_WIDTH_EXPANDED,
            'transition': 'margin-left 0.3s ease-in-out',
            'background-color': '#ffffff', # Pure white background for main content
            # 'min-height': f'calc(100vh - {NAVBAR_HEIGHT})',
            'height':'100%',
            'overflow-y': 'auto',
            'flex-grow': 1,
            'width': '100%',
            'padding-left': '20px',
            'padding-right': '20px'
        }),
    ], style={
        'display': 'flex',
        'height': f'calc(100vh - {NAVBAR_HEIGHT})',
        'width': '100vw',
        'box-sizing': 'border-box'
    })
])
# --- START - Dash Callbacks for left pane dropdowns ---
# --------------------------------
@callback(
    Output('school-filter-dropdown-co', 'options'),
    Input('intermediate-master-school-id', 'data')
)
def update_school_dropdown_options(master_school_id):
    """Updates the school dropdown options based on the selected master school."""
    filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
    available_schools = sorted(filtered_df['SchoolName'].unique())
    options = [{'label': school, 'value': school} for school in available_schools]
    return options


@callback(
    Output('department-filter-dropdown-co', 'options'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value')]
)
def update_department_dropdown_options(master_school_id, selected_schools):
    """Updates the department dropdown options based on the selected master school and schools."""
    filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    available_departments = sorted(filtered_df['DepartmentName'].unique())
    options = [{'label': department, 'value': department} for department in available_departments]
    return options


@callback(
    Output('program-filter-dropdown-co', 'options'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value')]
)
def update_program_dropdown_options(master_school_id, selected_schools, selected_departments):
    """Updates the program dropdown options based on the selected master school, schools, and departments."""
    filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    if selected_departments:
        filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]

    available_programs = sorted(filtered_df['ProgramName'].unique())
    options = [{'label': program, 'value': program} for program in available_programs]
    return options


# --- END - Dash Callbacks for left pane dropdowns ---
# --------------------------------


# --- Callback to update the course-selector dropdown ---
@callback(
    Output('course-selector', 'options'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_course_selector_options(master_school_id, selected_schools, selected_departments, selected_programs):
    try:
        filtered_df = get_filtered_courses(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df is None:
            raise Exception("Data not available.")
        courses_df_filtered = filtered_df[['CourseName', 'CourseId']].drop_duplicates()
        
        # Create options with full course names
        course_options = [
            {
                'label': row['CourseName'],  # Full course name
                'value': row['CourseId'],
                'title': row['CourseName']   # Add title for tooltip on hover
            } for _, row in courses_df_filtered.iterrows()
        ]
        return course_options
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        return []  # Return empty options in case of error
    
# --- Callback to toggle sidebar and adjust main content ---
@callback(
    Output('sidebar', 'style'),
    Output('sidebar-content', 'style'), # Control content visibility/opacity
    Output('filters-title', 'style'), # Control Filters title visibility/opacity
    Output('main-content', 'style'),
    Input('sidebar-toggle-button', 'n_clicks'),
    State('sidebar', 'style'),
    State('sidebar-content', 'style'),
    State('filters-title', 'style'),
    State('main-content', 'style'),
    prevent_initial_call=True
)
def toggle_sidebar(n_clicks, current_sidebar_style, current_content_style, current_title_style, current_main_style):
    # Check if sidebar is currently expanded based on its width
    is_expanded = current_sidebar_style.get('width') == SIDEBAR_WIDTH_EXPANDED

    new_sidebar_style = dict(current_sidebar_style)
    new_content_style = dict(current_content_style)
    new_title_style = dict(current_title_style)
    new_main_style = dict(current_main_style)

    if is_expanded:
        # Collapse the sidebar
        new_sidebar_style['width'] = SIDEBAR_WIDTH_COLLAPSED
        new_content_style['opacity'] = 0
        new_content_style['pointer-events'] = 'none' # Disable interaction when collapsed
        new_title_style['opacity'] = 0 # Hide "Filters" title
        new_main_style['margin-left'] = SIDEBAR_WIDTH_COLLAPSED
    else:
        # Expand the sidebar
        new_sidebar_style['width'] = SIDEBAR_WIDTH_EXPANDED
        new_content_style['opacity'] = 1
        new_content_style['pointer-events'] = 'auto' # Enable interaction when expanded
        new_title_style['opacity'] = 1 # Show "Filters" title
        new_main_style['margin-left'] = SIDEBAR_WIDTH_EXPANDED
    
    # Ensure transitions are maintained
    new_sidebar_style['transition'] = 'width 0.3s ease-in-out'
    new_content_style['transition'] = 'opacity 0.3s ease-in-out'
    new_title_style['transition'] = 'opacity 0.3s ease-in-out'
    new_main_style['transition'] = 'margin-left 0.3s ease-in-out'

    return new_sidebar_style, new_content_style, new_title_style, new_main_style

# Add these two callbacks to populate the section and CO dropdowns

# Callback to update section dropdown based on selected course
@callback(
    Output('section-selector', 'options'),
    [Input('course-selector', 'value'),
     Input('intermediate-master-school-id', 'data')]
)
def update_section_options(selected_course, master_school_id):
    if not selected_course:
        return []
    
    try:
        # Filter data for the selected course
        course_data = co_df[(co_df['CourseId'] == selected_course)]
        
        # Get unique sections
        sections = course_data[['SectionId', 'SectionName']].drop_duplicates()
        
        # Create options with full section names
        options = [
            {
                'label': row['SectionName'],
                'value': row['SectionId'],
                'title': row['SectionName']  # Add title for tooltip on hover
            } for _, row in sections.iterrows()
        ]
        
        return options
    except Exception as e:
        print(f"Error updating section options: {e}")
        return []

# Callback to update CO dropdown based on selected course and section
@callback(
    Output('co-selector', 'options'),
    [Input('course-selector', 'value'),
     Input('section-selector', 'value'),
     Input('intermediate-master-school-id', 'data')]
)
def update_co_options(selected_course, selected_section, master_school_id):
    if not selected_course:
        return []
    
    try:
        # Filter data for the selected course
        course_data = co_df[(co_df['CourseId'] == selected_course)]
        
        # Further filter by section if selected
        if selected_section:
            course_data = course_data[course_data['SectionId'] == selected_section]
        
        # Get unique COs
        cos = course_data[['COName', 'CODesc']].drop_duplicates()
        
        # Create options with descriptions
        options = [
            {
                'label': f"{row['COName']}",
                'value': row['COName'],
                'title': f"{row['COName']}"  # Add title for tooltip on hover
            } for _, row in cos.iterrows()
        ]
        
        return options
    except Exception as e:
        print(f"Error updating CO options: {e}")
        return []



# --- Start - Dash Callbacks for charts ---
# --------------------------------
# --- Callback to update the program-wise attainment chart ---
@callback(
    Output('pgmwise-attainment-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_pgmwise_attainment_chart(master_school_id, selected_schools, selected_departments, selected_programs):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        pgmwise_attainment_df = get_programwise_attainment(filtered_df)
        fig = get_programwise_attainment_chart(pgmwise_attainment_df)
        return fig
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        return {}  # Return an empty figure in case of error


# --- Callback to update the program wise attainment pct chart ---
@callback(
    Output('pgmwise-attainment-pct-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_pgmwise_pct_attainment_chart(master_school_id, selected_schools, selected_departments, selected_programs):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        pgmwise_attainment_df = get_programwise_attainment(filtered_df)
        fig = get_programwise_attainment_pct_chart(pgmwise_attainment_df)
        return fig
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        return {}  # Return an empty figure in case of error


# --- Callback to update the school wise attainment chart ---
@callback(
    Output('school-wise-attainment-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_school_wise_attainment_chart(master_school_id, selected_schools, selected_departments, selected_programs):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        level2_df = build_level2_df(filtered_df)
        fig = get_schoolwise_attainment_chart(level2_df)
        return fig
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        return {}  # Return an empty figure in case of error


# --- Callback to update the school wise attainment pct chart ---
@callback(
    Output('school-wise-attainment-pct-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_school_wise_attainment_pct_chart(master_school_id, selected_schools, selected_departments,
                                            selected_programs):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        level2_df = build_level2_df(filtered_df)
        fig = get_schoolwise_attainment_100chart(level2_df)
        return fig
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        return {}  # Return an empty figure in case of error


# --- Callback to update the school-wise-co-attainment-table ---
@callback(
    Output('schoolwise-co-attainment-table', 'columns'),
    Output('schoolwise-co-attainment-table', 'data'),
    Output('schoolwise-co-attainment-table', 'style_table'),
    Output('schoolwise-co-attainment-table', 'style_header'),
    Output('schoolwise-co-attainment-table', 'style_cell_conditional'),
    Output('schoolwise-co-attainment-table', 'style_data'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_schoolwise_co_attainment_table(master_school_id, selected_schools, selected_departments, selected_programs):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df is None:
            raise Exception("Data not available.")
        
        # Get the distribution data
        schoolwise_distr_df = get_schoolwise_attainment_distr(filtered_df).reset_index()
        
        # Define columns with proper names
        columns = [{"name": i, "id": i} for i in schoolwise_distr_df.columns]
        
        # Convert data to records
        data = schoolwise_distr_df.to_dict('records')
        
        # Define table styles
        style_table = {
            'overflowX': 'auto',
            'border': '1px solid #ddd',
            'borderRadius': '5px',
            'boxShadow': '0 2px 5px rgba(0,0,0,0.1)',
            'marginBottom': '20px'
        }
        
        style_header = {
            'backgroundColor': '#2c3e50',
            'color': 'white',
            'fontWeight': 'bold',
            'border': '1px solid #ddd',
            'padding': '10px'
        }
        
        # Left-align the first column (SchoolName)
        style_cell_conditional = [
            {
                'if': {'column_id': schoolwise_distr_df.columns[0]},  # First column
                'textAlign': 'left',
                'paddingLeft': '15px'
            }
        ]
        
        style_data = {
            'border': '1px solid #ddd',
            'padding': '8px',
            'backgroundColor': 'white',
            'color': '#333'
        }
        
        return columns, data, style_table, style_header, style_cell_conditional, style_data
    
    except Exception as e:
        print(f"Error during data processing operations: {e}")
        # Return empty values with default styling
        return [], [], {}, {}, [], {}  # Return empty columns and data in case of error


# Callback to update the main bar chart based on dropdown selection
@callback(
    Output('course-bar-chart', 'figure'),
    [Input('course-selector', 'value'),
     Input('section-selector', 'value'),
     Input('co-selector', 'value'),
     Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_course_chart(selected_group, selected_section, selected_co, master_school_id, selected_schools, selected_departments, selected_programs):
    if not selected_group:
        # Return a figure with a message when no course is selected
        return {
            "layout": {
                "xaxis": {"visible": False},
                "yaxis": {"visible": False},
                "annotations": [
                    {
                        "text": "Please select a course to view the chart",
                        "xref": "paper",
                        "yref": "paper",
                        "showarrow": False,
                        "font": {"size": 20, "color": "#7f7f7f"}
                    }
                ],
                "height": 400,
                "paper_bgcolor": "#f9f9f9",
                "plot_bgcolor": "#f9f9f9"
            }
        }
    
    if selected_group == 'no_chart':
        raise PreventUpdate
    elif selected_group in courses_df['CourseId'].values:
        co_attmt_level = 3
        if use_csv:
            course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                         selected_programs)
            course_df = course_df[(course_df['CourseId'] == selected_group) & (course_df['COAttainmentLevel'] == co_attmt_level)]
            
            # Apply section filter if selected
            if selected_section:
                course_df = course_df[course_df['SectionId'] == selected_section]
            
            # Apply CO filter if selected
            if selected_co:
                course_df = course_df[course_df['COName'] == selected_co]
                
        elif not use_csv:
            db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
            co_query = (
                f"""
                SELECT * 
                FROM dbo.COAttainmentComputedMetaDatas 
                WHERE COAttainmentLevel = {co_attmt_level} 
                  AND MasterSchoolId = {master_school_id} 
                  AND CourseId = {selected_group}
                """
            )
            
            # Add section filter if selected
            if selected_section:
                co_query += f" AND SectionId = {selected_section}"
                
            # Add CO filter if selected
            if selected_co:
                co_query += f" AND COName = '{selected_co}'"
                
            result = execute_sql_query(co_query, db_name)
            course_df = result
            
        # Create a copy of the dataframe to avoid modifying the original
        # Group by SectionName and CO Name and get average of attainment
        display_df = course_df.groupby(['SectionName', 'COName']).agg({
            'Attainment': 'mean',
            'SectionId': 'first',  # Keep the first SectionId for each group
            'CourseName': 'first',  # Keep the first CourseName for each group
            'CODesc': 'first',  # Keep the first CODesc for each group
            'Threshold': 'first'  # Keep the first Threshold for each group
        }).reset_index()
        
        
        # Debug: Print the attainment values to check what's in the data
        print("Attainment values in data:", display_df['Attainment'].unique())
        
        # Get the course name
        course_name = display_df['CourseName'].iloc[0] if not display_df.empty else "Unknown Course"
        
        # Calculate student count per section
        section_student_counts = {}
        if use_csv:
            student_level_data = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                                     selected_programs)
            student_level_data = student_level_data[(student_level_data['CourseId'] == selected_group) & 
                                                   (student_level_data['COAttainmentLevel'] == 3)]
            if not student_level_data.empty:
                student_counts = student_level_data.groupby(['SectionId', 'SectionName'])['StudentId'].nunique().reset_index()
                student_counts.rename(columns={'StudentId': 'StudentCount'}, inplace=True)
                for _, row in student_counts.iterrows():
                    section_student_counts[row['SectionId']] = row['StudentCount']
        elif not use_csv:
            db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
            student_query = (
                f"""
                SELECT SectionId, SectionName, COUNT(DISTINCT StudentId) as StudentCount 
                FROM dbo.COAttainmentComputedMetaDatas 
                WHERE COAttainmentLevel = 2 
                  AND MasterSchoolId = {master_school_id} 
                  AND CourseId = {selected_group}
                GROUP BY SectionId, SectionName
                """
            )
            student_counts = execute_sql_query(student_query, db_name)
            if student_counts is not None:
                for _, row in student_counts.iterrows():
                    section_student_counts[row['SectionId']] = row['StudentCount']
        
        # Trim section names to last 25 characters
        display_df['TrimmedSectionName'] = display_df['SectionName'].apply(
            lambda x: f"...{x[-25:]}" if len(x) > 25 else x
        )
        
        # Create a dictionary to map CO names to their descriptions
        co_desc_dict = {}
        for _, row in display_df.drop_duplicates(['COName', 'CODesc']).iterrows():
            co_desc_dict[row['COName']] = row['CODesc']

        # Create a custom x-axis with section IDs as values but trimmed names as labels
        unique_sections = display_df[['SectionId', 'SectionName', 'TrimmedSectionName']].drop_duplicates()
        x_values = unique_sections['SectionId'].tolist()
        x_labels = unique_sections['TrimmedSectionName'].tolist()

        # Create figure with explicit data
        fig = go.Figure()
        color_index = 0
        
        # Get unique CO names
        co_names = display_df['COName'].unique()
        
        # For each CO, create a bar trace
        for co_name in co_names:
            co_data = display_df[display_df['COName'] == co_name]
            co_desc = co_desc_dict.get(co_name, "")
            
            # Create bar trace with explicit y values
            fig.add_trace(go.Bar(
                x=co_data['SectionId'],
                y=co_data['Attainment'].astype(float),  # Explicitly convert to float
                name=co_name,
                marker_color=colors[color_index % len(colors)],
                hovertext=[
                    f"<b>Section:</b> {section}<br><b>Students:</b> {section_student_counts.get(section_id, 0)}<br><b>{co_name}:</b> {co_desc}" 
                    for section, section_id in zip(co_data['SectionName'], co_data['SectionId'])
                ],
                hovertemplate='%{hovertext}<br>Attainment: %{y:.1f}%<extra></extra>'
            ))
            color_index += 1
        
        # Add threshold lines
        threshold_data = display_df.groupby(['SectionId', 'COName'])['Threshold'].mean().reset_index()
        
        for co_name in co_names:
            co_threshold = threshold_data[threshold_data['COName'] == co_name]
            
            fig.add_trace(go.Scatter(
                x=co_threshold['SectionId'],
                y=co_threshold['Threshold'],
                mode='lines+markers',
                name=f'Threshold - {co_name}',
                line=dict(color='#E05D5D', width=2, dash='dot'),
                marker=dict(color='#E05D5D', symbol='diamond', size=8)
            ))

        # Update layout
        fig.update_layout(
            barmode='group',
            title=f'Attainment by Sections and Course Outcomes<br>Course: {course_name}',
            xaxis=dict(
                title='Section Name',
                tickmode='array',
                tickvals=x_values,
                ticktext=x_labels,
                tickangle=-45
            ),
            yaxis=dict(
                title="CO Attainment Percentage",
                range=[0, 100]  # Explicitly set y-axis range
            ),
            legend_title="Course Outcomes"
        )
        
        return fig
    else:
        return go.Figure()  # Return empty figure if group not found


@callback(
    Output('course-assgn-bar-chart', 'figure'),
    [Input('course-selector', 'value'),
     Input('section-selector', 'value'),
     Input('co-selector', 'value'),
     Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_course_assgn_chart(selected_group, selected_section, selected_co, master_school_id, selected_schools, selected_departments, selected_programs):
    if not selected_group:
        # Return a figure with a message when no course is selected
        return {
            "layout": {
                "xaxis": {"visible": False},
                "yaxis": {"visible": False},
                "annotations": [
                    {
                        "text": "Please select a course to view the chart",
                        "xref": "paper",
                        "yref": "paper",
                        "showarrow": False,
                        "font": {"size": 20, "color": "#7f7f7f"}
                    }
                ],
                "height": 400,
                "paper_bgcolor": "#f9f9f9",
                "plot_bgcolor": "#f9f9f9"
            }
        }
    
    if selected_group == 'no_chart':
        raise PreventUpdate
    elif selected_group in courses_df['CourseId'].values:
        co_attmt_level = 0
        if use_csv:
            course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                         selected_programs)
            course_df = course_df[(course_df['CourseId'] == selected_group) & (course_df['COAttainmentLevel'] == co_attmt_level)]
            
            # Apply section filter if selected
            if selected_section:
                course_df = course_df[course_df['SectionId'] == selected_section]
            
            # Apply CO filter if selected
            if selected_co:
                course_df = course_df[course_df['COName'] == selected_co]
                
        elif not use_csv:
            db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
            co_query = (
                f"""
                SELECT * 
                FROM dbo.COAttainmentComputedMetaDatas 
                WHERE COAttainmentLevel = {co_attmt_level} 
                    AND MasterSchoolId = {master_school_id} 
                    AND CourseId = {selected_group}
                """
            )
            
            # Add section filter if selected
            if selected_section:
                co_query += f" AND SectionId = {selected_section}"
                
            # Add CO filter if selected
            if selected_co:
                co_query += f" AND COName = '{selected_co}'"
                
            result = execute_sql_query(co_query, db_name)
            course_df = result
            
        if len(course_df) > 0:
            course_name = course_df['CourseName'].iloc[0]
            dim_assgn = 'AssignmentName'
            measure_col = 'Attainment'
            
            
            # Group by assignment and calculate mean attainment and threshold
            grouped_df = course_df.groupby('AssignmentName').agg(
                Attainment=('Attainment', 'mean'),
                Threshold=('Threshold', 'mean')
            ).reset_index()
            
            # Create a figure with colors from the colors array
            fig = go.Figure()
            
            # Add individual bars with different colors from the colors array
            for i, (idx, row) in enumerate(grouped_df.iterrows()):
                fig.add_trace(go.Bar(
                    x=[row['AssignmentName']],
                    y=[row['Attainment']],
                    name=row['AssignmentName'],
                    marker_color=colors[i % len(colors)],  # Use different colors from the colors array
                    showlegend=False  # Hide from legend
                ))
            
            # Add threshold line using a color from the colors array
            fig.add_trace(go.Scatter(
                x=grouped_df['AssignmentName'],
                y=grouped_df['Threshold'],
                mode='lines+markers',
                name='Threshold',
                line=dict(color='#E05D5D', width=2),  # Use a distinct color for threshold
                marker=dict(color='#E05D5D'),  # Use same color for markers
                showlegend=True  # Show in legend
            ))
            
            fig.update_layout(
                title=f'Attainment by Assessment Tools<br>Course: {course_name}',
                xaxis_title="Assessment Tools",
                yaxis_title="CO Attainment Percentage",
                yaxis_range=[0, 100],
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="right",
                    x=0.99
                )
            )
            
            return fig
    else:
        return go.Figure()  # Return empty figure if group not found (shouldn't happen with our data)


# Callback to update drilldown chart based on click in the main bar chart
@callback(
    Output('sect-student-co-attainment-chart', 'figure'),
    [Input('course-selector', 'value'),
     Input('section-selector', 'value'),
     Input('co-selector', 'value'),
     Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value')]
)
def update_sect_student_co_chart(selected_course, selected_section, selected_co, master_school_id, selected_schools, 
                                 selected_departments, selected_programs):   
    # Only proceed if both course and section are selected
    if not selected_course or not selected_section:
        return {
            "layout": {
                "xaxis": {"visible": False},
                "yaxis": {"visible": False},
                "annotations": [
                    {
                        "text": "Please select both a course and a section to view student-level data",
                        "xref": "paper",
                        "yref": "paper",
                        "showarrow": False,
                        "font": {"size": 16}
                    }
                ],
                "height": 400,
                "paper_bgcolor": "#f9f9f9",
                "plot_bgcolor": "#f9f9f9"
            }
        }
    
    # Proceed with data retrieval
    co_attmt_level = 2  # Student level
    
    if use_csv:
        # Get filtered dataset
        student_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                        selected_programs)
        # Apply course and section filters
        student_df = student_df[(student_df['CourseId'] == selected_course) & 
                               (student_df['SectionId'] == selected_section) &
                               (student_df['COAttainmentLevel'] == co_attmt_level)]
        
        # Apply CO filter if selected
        if selected_co:
            student_df = student_df[student_df['COName'] == selected_co]
            
    elif not use_csv:
        db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
        co_query = (
            f"""
            SELECT * 
            FROM dbo.COAttainmentComputedMetaDatas 
            WHERE COAttainmentLevel = {co_attmt_level} 
                AND MasterSchoolId = {master_school_id} 
                AND CourseId = {selected_course}
                AND SectionId = {selected_section}
            """
        )
        
        # Add CO filter if selected
        if selected_co:
            co_query += f" AND COName = '{selected_co}'"
            
        result = execute_sql_query(co_query, db_name)
        student_df = result
    
    # Check if we have data
    if student_df is None or student_df.empty:
        return {
            "layout": {
                "xaxis": {"visible": False},
                "yaxis": {"visible": False},
                "annotations": [
                    {
                        "text": "No student-level data available for the selected course and section",
                        "xref": "paper",
                        "yref": "paper",
                        "showarrow": False,
                        "font": {"size": 16}
                    }
                ]
            }
        }
    
    # Get section name for display
    section_name = student_df['SectionName'].iloc[0]
    course_name = student_df['CourseName'].iloc[0]
    
    # Create a dictionary to map CO names to their descriptions
    co_desc_dict = {}
    for _, row in student_df.drop_duplicates(['COName', 'CODesc']).iterrows():
        co_desc_dict[row['COName']] = row['CODesc']
    
    # Get unique students and create a mapping for display
    students = student_df[['StudentId', 'StudentName']].drop_duplicates()
    
    # Sort students by name to ensure consistent ordering
    students = students.sort_values('StudentName')
    
    # Create a shortened version of student names for x-axis display
    students['DisplayName'] = students['StudentName'].apply(
        lambda x: f"{x[:15]}..." if len(x) > 15 else x
    )
    
    # Create a mapping from StudentId to DisplayName and full StudentName
    student_display_map = students.set_index('StudentId')['DisplayName'].to_dict()
    student_name_map = students.set_index('StudentId')['StudentName'].to_dict()
    
    # Get ordered list of student IDs to ensure consistent x-axis
    ordered_student_ids = students['StudentId'].tolist()
    
    # Create figure
    fig = go.Figure()
    color_index = 0
    
    # Get unique CO names
    co_names = student_df['COName'].unique()
    
    # For each CO, create a bar trace
    for co_name in co_names:
        co_data = student_df[student_df['COName'] == co_name]
        co_desc = co_desc_dict.get(co_name, "")
        
        # Create a complete dataset with all students for this CO
        # This ensures no gaps in the bar chart
        complete_data = []
        complete_y = []
        complete_hover = []
        
        for student_id in ordered_student_ids:
            student_co_data = co_data[co_data['StudentId'] == student_id]
            if not student_co_data.empty:
                attainment = student_co_data['Attainment'].iloc[0]
            else:
                attainment = None  # No data for this student-CO combination
                
            complete_data.append(student_id)
            complete_y.append(attainment)
            complete_hover.append(
                f"<b>Student:</b> {student_name_map.get(student_id, 'Unknown')}<br>" +
                f"<b>ID:</b> {student_id}<br>" +
                f"<b>{co_name}:</b> {co_desc}"
            )
        
        # Create bar trace with complete data
        fig.add_trace(go.Bar(
            x=complete_data,
            y=complete_y,
            name=co_name,
            marker_color=colors[color_index % len(colors)],
            hovertext=complete_hover,
            hovertemplate='%{hovertext}<br>Attainment: %{y:.1f}%<extra></extra>'
        ))
        color_index += 1
    
    # Add threshold lines
    threshold_data = student_df.groupby(['StudentId', 'COName'])['Threshold'].mean().reset_index()
    
    for co_name in co_names:
        co_threshold = threshold_data[threshold_data['COName'] == co_name]
        
        # Create complete threshold data for all students
        complete_threshold_x = ordered_student_ids
        complete_threshold_y = []
        
        for student_id in ordered_student_ids:
            student_threshold = co_threshold[co_threshold['StudentId'] == student_id]
            if not student_threshold.empty:
                threshold_value = student_threshold['Threshold'].iloc[0]
            else:
                # Use the average threshold for this CO if no specific value for this student
                threshold_value = co_threshold['Threshold'].mean() if not co_threshold.empty else 50
                
            complete_threshold_y.append(threshold_value)
        
        fig.add_trace(go.Scatter(
            x=complete_threshold_x,
            y=complete_threshold_y,
            mode='lines',
            name=f'Threshold - {co_name}',
            line=dict(color='#E05D5D', width=2, dash='dot')
        ))
    
    # Update layout
    fig.update_layout(
        barmode='group',
        title=f'Student-Level CO Attainment<br> Section: {section_name}',
        xaxis=dict(
            title='Student',
            tickangle=-45,
            # Use student names instead of IDs for x-axis labels
            tickmode='array',
            tickvals=ordered_student_ids,
            ticktext=[student_display_map.get(id, id) for id in ordered_student_ids],
            categoryorder='array',
            categoryarray=ordered_student_ids,
            # Force uniform spacing between categories
            type='category',
            # Remove extra padding
            rangemode='normal',
            automargin=True
        ),
        yaxis=dict(
            title="CO Attainment Percentage",
            range=[0, 100]
        ),
        legend_title="Course Outcomes",
        # Significantly reduce spacing between bars
        bargap=0.15,        # Gap between bars of adjacent location coordinates (smaller value)
        bargroupgap=0.05,   # Gap between bars of the same location coordinates (smaller value),
        # Make plot more compact
        margin=dict(l=50, r=50, t=100, b=100),
        # Ensure bars are evenly distributed
        uniformtext=dict(mode='hide', minsize=8),
        # Force plot to use all available width
        autosize=True
    )
    
    return fig


@callback(
    Output('unattained-cos-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value'),
     Input('unattained-cos-limit', 'value')]  # New input for the limit
)
def update_unattained_cos_chart(master_school_id, selected_schools, selected_departments, selected_programs, limit):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        # Use the selected limit or default to 50
        top_limit = limit if limit else 20
        
        # Get top unattained COs with the specified limit
        top_unattained_cos = get_top_unattained_cos(filtered_df, top_limit)
        fig = get_unattained_cos_chart(top_unattained_cos)
        return fig
    except Exception as e:
        print(f"Error during data processing for unattained COs chart: {e}")
        return {}  # Return an empty figure in case of error


@callback(
    Output('lowest-performing-courses-chart', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown-co', 'value'),
     Input('department-filter-dropdown-co', 'value'),
     Input('program-filter-dropdown-co', 'value'),
     Input('lowest-courses-limit', 'value')]  # New input for the limit
)
def update_lowest_performing_courses_chart(master_school_id, selected_schools, selected_departments, selected_programs, limit):
    try:
        filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                           selected_programs)
        if filtered_df.empty:
            raise Exception("Data not available.")

        # Use the selected limit or default to 20
        courses_limit = limit if limit else 20
        
        lowest_courses = get_lowest_performing_courses(filtered_df, courses_limit)
        fig = get_lowest_performing_courses_chart(lowest_courses)
        return fig
    except Exception as e:
        print(f"Error during data processing: {e}")
        return {}  # Return empty figure in case of error


# --- END - Dash Callbacks for charts ---
# --------------------------------


# @callback(
#     Output('course-co-bar-chart', 'figure'),
#     [Input('course-selector', 'value'),
#      Input('intermediate-master-school-id', 'data'),
#      Input('school-filter-dropdown-co', 'value'),
#      Input('department-filter-dropdown-co', 'value'),
#      Input('program-filter-dropdown-co', 'value')]
# )
# def update_course_co_chart(selected_group, master_school_id, selected_schools, selected_departments, selected_programs):
#     if selected_group == 'no_chart':
#         raise PreventUpdate
#         return {  # Return empty figure if 'No Chart' is selected            
#             "layout": {
#                 "xaxis": {"visible": False},
#                 "yaxis": {"visible": False},
#                 "annotations": [
#                     {
#                         "text": str(selected_group),
#                         "xref": "paper",
#                         "yref": "paper",
#                         "showarrow": False,
#                         "font": {"size": 20}
#                     }
#                 ]
#             }
#         }
#     elif selected_group in courses_df['CourseId'].values:
#         co_attmt_level = 0
#         if use_csv:
#             course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
#                                             selected_programs)
#             course_df = course_df[(course_df['CourseId'] == selected_group) 
#                                   & (course_df['COAttainmentLevel'] == co_attmt_level)]
#         elif not use_csv:
#             db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
#             co_query = (
#                 f"""
#                 SELECT * 
#                 FROM dbo.COAttainmentComputedMetaDatas 
#                 WHERE COAttainmentLevel = {co_attmt_level} 
#                   AND MasterSchoolId = {master_school_id} 
#                   AND CourseId = {selected_group}
#                 """
#             )
#             result = execute_sql_query(co_query, db_name)
#             course_df = result
            
#         if len(course_df) > 0:
#             course_name = course_df['CourseName'].iloc[0]
#             # Group by CO and calculate mean attainment and threshold
#             grouped_df = course_df.groupby('COName').agg(
#                 Attainment=('Attainment', 'mean'),
#                 Threshold=('Threshold', 'mean')
#             ).reset_index()
            
#             fig = go.Figure()
            
#             # Add individual bars with different colors from the colors array
#             for i, (idx, row) in enumerate(grouped_df.iterrows()):
#                 fig.add_trace(go.Bar(
#                     x=[row['COName']],
#                     y=[row['Attainment']],
#                     name=row['COName'],
#                     marker_color=colors[i % len(colors)],  # Use different colors from the colors array
#                     showlegend=False  # Hide from legend
#                 ))
            
#             # Add threshold line using a color from the colors array
#             fig.add_trace(go.Scatter(
#                 x=grouped_df['COName'],
#                 y=grouped_df['Threshold'],
#                 mode='lines+markers',
#                 name='Threshold',
#                 line=dict(color='#E05D5D', width=2),  # Use a distinct color for threshold
#                 marker=dict(color='#E05D5D'),  # Use same color for markers
#                 showlegend=True  # Show in legend
#             ))
            
#             fig.update_layout(
#                 title=f'Attainment by Course Outcome<br>Course: {course_name}',
#                 xaxis_title="CO Name",
#                 yaxis_title="CO Attainment Percentage",
#                 yaxis_range=[0, 100],
#                 legend=dict(
#                     yanchor="top",
#                     y=0.99,
#                     xanchor="right",
#                     x=0.99
#                 )
#             )
            
#             return fig
#         else:
#             return go.Figure()  # Return empty figure if no data found
#     else:
#         return go.Figure()  # Return empty figure if group not found


# Callback to update drilldown chart based on click in the main bar chart
# @callback(
#     Output('sect-assgn-co-attainment-chart', 'figure'),
#     [Input('course-bar-chart', 'clickData'),
#      Input('course-selector', 'value'),
#      Input('intermediate-master-school-id', 'data'),
#      Input('school-filter-dropdown-co', 'value'),
#      Input('department-filter-dropdown-co', 'value'),
#      Input('program-filter-dropdown-co', 'value')]
# )
# def update_sect_assgn_co_chart(clickData, selected_group, master_school_id, selected_schools, selected_departments, selected_programs):   
#     if clickData:
#         co_attmt_level = 0
#         clicked_sectionId = clickData['points'][0]['x']
#         if use_csv:
#             section_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
#                                             selected_programs)
#         elif not use_csv:
#             db_name = master_school_df[master_school_df['MasterSchoolId'] == master_school_id]['DBName'].values[0] 
#             co_query = (
#                 f"""
#                 SELECT * 
#                 FROM dbo.COAttainmentComputedMetaDatas 
#                 WHERE COAttainmentLevel = {co_attmt_level} 
#                     AND MasterSchoolId = {master_school_id} 
#                     AND CourseId = {selected_group}
#                     AND SectionId = {clicked_sectionId}
#                 """
#             )
#             result = execute_sql_query(co_query, db_name)
#             section_df = result  

#         if clicked_sectionId in co_df['CourseId']:
#             section_df = section_df[
#                 (section_df['SectionId'] == clicked_sectionId) & (section_df['COAttainmentLevel'] == co_attmt_level)]
#             subcat_co = section_df['COName'].unique()
#             clicked_sectionName = section_df['SectionName'].unique()[0]
#             clicked_CoId = clickData['points'][0]['curveNumber']
#             clicked_co = subcat_co[clicked_CoId]  # Get the category name from the clicked bar
#             print(f"Category is {clicked_sectionId} and subcat: {clicked_co} .")
#             drilldown_df = section_df[section_df['COName'] == clicked_co]

#             grouped_df = drilldown_df.groupby('AssignmentName').agg(
#                 Attainment=('Attainment', 'mean'),
#                 Threshold=('Threshold', 'mean')
#                 ).reset_index()
            
#             # Create a figure with colors from the colors array
#             fig = go.Figure()
            
#             # Add bar trace for attainment with color from colors array
#             fig.add_trace(go.Bar(
#                 x=grouped_df['AssignmentName'],
#                 y=grouped_df['Attainment'],
#                 name='Attainment',
#                 marker_color=colors[0]  # Use first color from colors array
#             ))
            
#             # Add line trace for threshold with color from colors array
#             fig.add_trace(go.Scatter(
#                 x=grouped_df['AssignmentName'],
#                 y=grouped_df['Threshold'],
#                 mode='lines+markers',
#                 name='Threshold',
#                 line=dict(color=colors[4]),  # Use fourth color from colors array
#                 marker=dict(color=colors[4])  # Use fourth color from colors array
#             ))
            
#             fig.update_layout(
#                 title=f'Assessment Tools <br />Section : {clicked_sectionName}<br /> CO : {clicked_co}',
#                 xaxis_title="Assessment Tools",
#                 yaxis_title="CO Attainment Percentage",
#                 legend_title="Assessment Tools",
#                 yaxis_range=[0, 100]
#             )
            
#             return fig
#         else:
#             return {  # Return an empty figure or a message if no drilldown data
#                 "layout": {
#                     "xaxis": {"visible": False},
#                     "yaxis": {"visible": False},
#                     "annotations": [
#                         {
#                             "text": f"No drilldown data for {clicked_sectionId}",
#                             "xref": "paper",
#                             "yref": "paper",
#                             "showarrow": False,
#                             "font": {"size": 12}
#                         }
#                     ]
#                 }
#             }
#     else:
#         return go.Figure()  # Return empty figure if no bar is clicked (initially and when clicking outside bars)
