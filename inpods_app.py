import dash
from dash import Dash, html, dcc, Input, Output
from dash.exceptions import PreventUpdate
import dash_core_components as dcc
import dash_html_components as html
from flask_login import current_user, logout_user
import pandas as pd

# Define styles
header_style = {
    'display': 'flex',
    'justify-content': 'space-between',
    'align-items': 'center',
    'padding': '10px 20px',
    'background-color': '#343a40',
    'color': 'white',
    'margin-bottom': '20px'
}

title_style = {
    'margin': '0',
    'font-size': '24px',
    'z-index': '1060',
}

user_info_style = {
    'display': 'flex',
    'align-items': 'center',
    'gap': '15px'
}

logout_button_style = {
    'background-color': '#e74c3c',
    'color': 'white',
    'border': 'none',
    'padding': '8px 15px',
    'border-radius': '4px',
    'cursor': 'pointer'
}


def create_inpods_app(server):
    # Create Dash app
    app = Dash(__name__, 
               server=server, 
               url_base_pathname='/inpods_dashboard/', 
               use_pages=True,
               external_stylesheets=[
                   'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css'
               ])
    app.config.suppress_callback_exceptions = True

    try:
        df = pd.read_csv('pages/master-schools.csv')
    except FileNotFoundError as e:
        print(f"Error: One or more data files not found: {e}")

    master_school_df = df[['MasterSchoolName', 'MasterSchoolId', 'DBName']].drop_duplicates()
    master_school_options = [
        {'label': row['MasterSchoolName'], 'value': row['MasterSchoolId'], 'dbname': row['DBName']} for _, row in master_school_df.iterrows()
    ]

    # Define the dashboard layout
    app.layout = html.Div([
        dcc.Location(id='url2', refresh=True),
        # Header with user info and logout button
        html.Div([
            html.H1('Outcomes Attainment Analysis', style=title_style ),
            html.Div(id='user-info', style=user_info_style)
        ], style=header_style),

        # Main content
        html.Div([
            html.Div([
                dcc.Dropdown(
                    id='ip-master-school-id',
                    value=303,  # Set default value to 303
                    options=master_school_options,
                )
            ], style={'width': '48%', 'display': 'none'}),  # Changed from 'inline-block' to 'none'
            # html.Div(id='op-master-school-id'),
            # html.Div([
            #     html.Div(
            #         dcc.Link(f"{page['name']} - {page['path']}", href=page["relative_path"])
            #     ) for page in dash.page_registry.values()
            # ]),
            dcc.Store(id='intermediate-master-school-id', data=[], storage_type='memory'),
            dash.page_container
        ])

    ])

    # Callback to display user info
    @app.callback(
        Output('user-info', 'children'),
        Input('user-info', 'id')
    )
    def update_user2_info(_):
        if current_user.is_authenticated:
            return [
                html.Div([
                    html.Span(f'Welcome, {current_user.first_name} {current_user.last_name}'),
                    html.Div(f'{current_user.id}', style={'font-size': '12px'})
                ]),
                html.Button('Logout', id='logout-button2', n_clicks=0, style=logout_button_style)
            ]
        return html.Div([
            html.H3('You are not logged in.')
        ])

    # Callback for logout button
    @app.callback(
        Output('url2', 'pathname'),
        Input('logout-button2', 'n_clicks'),
        prevent_initial_call=True
    )
    def logout2(n_clicks):
        if n_clicks > 0:
            logout_user()
            return '/login'
        raise PreventUpdate

    @app.callback(
        Output('intermediate-master-school-id', 'data'),
        Input('ip-master-school-id', 'value'))
    def store_master_school_id(master_school_id):
        return master_school_id

    # @app.callback(
    #     Output('op-master-school-id', 'children'),
    #     Input('intermediate-master-school-id', 'data'))
    # def update_master_school_id(master_school_id):
    #     return f'Output: {master_school_id}'

    return app
