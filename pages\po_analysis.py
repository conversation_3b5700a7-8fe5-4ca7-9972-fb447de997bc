import dash
from dash import html, dcc, callback, Input, Output, State
import plotly.io as pio
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from dash import dash_table
import os
from openai import AzureOpenAI
from db_connector import DatabaseConnector

dash.register_page(__name__, path='/po_analysis')
pio.templates.default = "seaborn"
# --- Variables ---
# co_df = None
# po_df = None
use_csv = False

# --- Data Loading ---
if use_csv:
    print("In Use csv")
    # try:
    #     co_df = pd.read_csv('pages/GU_CO_attainment.csv')
    # except FileNotFoundError as e:
    #     print(f"Error: One or more data files not found: {e}")

    # try:
    #     po_df = pd.read_csv('pages/GU_PO_attainment.csv')
    # except FileNotFoundError as e:
    #     print(f"Error: One or more data files not found: {e}")

else:
    # --- Database Connection ---
    # Replace with your actual credentials
    DB_SERVER = "20.242.124.131"
    DB_NAME = "ChitkaraPB_prodDB"
    DB_USER = "datafiReader"
    DB_PASSWORD = "P96Bry3rnGAv#9z8&f19tgS5reX4MU&T"

    db_connector = DatabaseConnector(DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD)
    try:
        db_connector.connect()
        # --- Data Loading and Preprocessing ---
        # co_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 2"
        # co_df = db_connector.execute_query(co_query)
        # co_df = co_df.rename(columns={'Attainmemt': 'Attainment'})
        # if co_df is None:
        #     raise Exception("Failed to load CO data from database.")

        po_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 7"
        po_df = db_connector.execute_query(po_query)
        po_df = po_df.rename(columns={'Attainmemt': 'Attainment'})
        if po_df is None:
            raise Exception("Failed to load PO data from database.")
    except Exception as e:
        print(f"Error during database operations: {e}")


# --- START - Helper Functions ---
# --------------------------------
def get_AI_client():
    endpoint = "https://ai-inpodsoutcomesanalysis01667696755442.openai.azure.com/"
    model_name = "gpt-35-turbo-16k"
    deployment = "gpt-35-turbo-16k"

    subscription_key = "6hLxnG751HGjuvx8iH2ZKZJRVIwvvP4rIwCvUHAkDeLMk5eLZkHFJQQJ99BCACHYHv6XJ3w3AAAAACOG8Xbv"
    api_version = "2024-12-01-preview"

    client = AzureOpenAI(
        api_version=api_version,
        azure_endpoint=endpoint,
        api_key=subscription_key,
    )
    return client


def get_outcomes_from_csv(filename):
    dtypes = {
        "Attainment": 'float',
        "Target": 'float',
        "Threshold": 'float'
    }
    outcomes_df = pd.read_csv(filename, encoding='utf8', low_memory=False, skipinitialspace=True, dtype=dtypes,
                              keep_default_na=True)
    values = {"Threshold": 0, "CourseId": 0}
    outcomes_df.fillna(values)
    outcomes_df['CourseId'] = 0
    return (outcomes_df)


def filter_outcomes_data(df, level):
    filtered_df = df[df['COAttainmentLevel'] == level]
    return filtered_df


def get_copo_attainment(df):
    metadata_df = df[
        ['MasterSchoolId', 'SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName',
         'BatchId', 'BatchName',
         'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'COId', 'COName', 'CODesc', 'POId', 'POName',
         'PODesc']].drop_duplicates()
    copo_df = df[['COId', 'COName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target',
                  'Threshold']].drop_duplicates()
    copo_attain_df = copo_df.groupby(['COId', 'COName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
    merged_df = pd.merge(copo_attain_df, metadata_df, how='inner',
                         left_on=['COId', 'COName', 'POId', 'POName'],
                         right_on=['COId', 'COName', 'POId', 'POName'])
    return merged_df


def get_schoolwise_attainment(merged_df):
    schoolwise_attainment = merged_df[
        ['SchoolId', 'SchoolName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['SchoolId', 'SchoolName']).mean(numeric_only=True).reset_index()
    return schoolwise_attainment


def get_schoolwise_attainment_chart(schoolwise_attainment):
    fig = px.bar(schoolwise_attainment, x="SchoolName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL'],
                 barmode='group',
                 labels={'SchoolName': 'School', 'POAttDWOAL': 'PO Attainment (Direct) without affinity',
                         'POAttDIWOAL': ' PO Attainment (Direct and Indirect) without affinity',
                         'POAttDWAL': 'PO Attainment (Direct) with affinity',
                         'POAttDIWAL': 'PO Attainment (Direct and Indirect) with affinity'}, template='seaborn')
    fig.update_layout(legend_title_text='Attainment Type')
    fig.update_traces(name='PO Attainment (Direct) without affinity', selector={'name': 'POAttDWOAL'})
    fig.update_traces(name='PO Attainment (Direct and Indirect) without affinity', selector={'name': 'POAttDIWOAL'})
    fig.update_traces(name='PO Attainment (Direct) with affinity', selector={'name': 'POAttDWAL'})
    fig.update_traces(name='PO Attainment (Direct and Indirect) with affinity', selector={'name': 'POAttDIWAL'})
    fig.update_layout(height=600)
    return fig


def get_program_wise_attainment(filtered_df):
    pgm_wise_attain_df = filtered_df[
        ['ProgramId', 'ProgramName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target',
         'SchoolName']].groupby(
        ['ProgramId', 'ProgramName', 'SchoolName']).mean(numeric_only=True).reset_index()
    metadata_df = filtered_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName']].drop_duplicates()
    # merged_df = pd.merge(pgm_wise_attain_df, metadata_df, how='inner',
    #                      left_on=['ProgramId', 'ProgramName'], right_on=['ProgramId', 'ProgramName'])
    return pgm_wise_attain_df


def get_programwise_attainment_chart(pgmwise_attainment):
    fig = px.bar(pgmwise_attainment, x="ProgramName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL'],
                 barmode='stack',
                 labels={'ProgramName': 'Program', 'POAttDWOAL': 'PO Attainment (Direct) without affinity',
                         'POAttDIWOAL': ' PO Attainment (Direct and Indirect) without affinity',
                         'POAttDWAL': 'PO Attainment (Direct) with affinity',
                         'POAttDIWAL': 'PO Attainment (Direct and Indirect) with affinity'},
                 template='seaborn')
    fig.update_layout(legend_title_text='Attainment Type')
    fig.update_traces(name='PO Attainment (Direct) without affinity', selector={'name': 'POAttDWOAL'})
    fig.update_traces(name='PO Attainment (Direct and Indirect) without affinity', selector={'name': 'POAttDIWOAL'})
    fig.update_traces(name='PO Attainment (Direct) with affinity', selector={'name': 'POAttDWAL'})
    fig.update_traces(name='PO Attainment (Direct and Indirect) with affinity', selector={'name': 'POAttDIWAL'})
    # Add target markers
    # fig.update_layout(xaxis={'categoryorder':'total descending'})
    fig.update_layout(height=600)
    return fig


def get_program_batch_wise_attainment(filtered_df):
    program_batch_wise_attainment = filtered_df[
        ['ProgramId', 'ProgramName', 'BatchId', 'BatchName', 'POAttDWOAL',
         'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['ProgramId', 'ProgramName', 'BatchId', 'BatchName']).mean(numeric_only=True).reset_index()
    return program_batch_wise_attainment


def get_program_batch_wise_attainment_chart(program_batch_wise_attainment):
    fig = go.Figure()
    program_batch_wise_attainment = program_batch_wise_attainment.sort_values(by=['ProgramName', 'BatchName'],
                                                                              ascending=[True, True])
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_batch_wise_attainment['ProgramName'], program_batch_wise_attainment['BatchName']],
        y=program_batch_wise_attainment['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainment across Program Batches ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category descending'})
    fig.update_layout(height=600)
    return fig


def get_program_dept_wise_attainment(filtered_df):
    program_dept_wise_attainment = filtered_df[['ProgramId', 'ProgramName', 'DepartmentId', 'DepartmentName',
                                                'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL',
                                                'Target']].groupby(
        ['ProgramId', 'ProgramName', 'DepartmentId', 'DepartmentName']).mean(numeric_only=True).reset_index()
    return program_dept_wise_attainment


def get_program_dept_wise_attainment_chart(program_dept_wise_attainment):
    fig = go.Figure()
    program_dept_wise_attainment = program_dept_wise_attainment.sort_values(by=['ProgramName', 'DepartmentName'],
                                                                            ascending=[True, True])
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['ProgramName'], program_dept_wise_attainment['DepartmentName']],
        y=program_dept_wise_attainment['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['ProgramName'], program_dept_wise_attainment['DepartmentName']],
        y=program_dept_wise_attainment['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))

    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['ProgramName'], program_dept_wise_attainment['DepartmentName']],
        y=program_dept_wise_attainment['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['ProgramName'], program_dept_wise_attainment['DepartmentName']],
        y=program_dept_wise_attainment['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainment across Program Departments ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category descending'})
    fig.update_layout(height=600)
    return fig


def get_dept_pgm_wise_attainment_chart(program_dept_wise_attainment):
    fig = go.Figure()
    program_dept_wise_attainment = program_dept_wise_attainment.sort_values(by=['ProgramName', 'DepartmentName'],
                                                                            ascending=[True, True])
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['DepartmentName'], program_dept_wise_attainment['ProgramName']],
        y=program_dept_wise_attainment['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['DepartmentName'], program_dept_wise_attainment['ProgramName']],
        y=program_dept_wise_attainment['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['DepartmentName'], program_dept_wise_attainment['ProgramName']],
        y=program_dept_wise_attainment['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[program_dept_wise_attainment['DepartmentName'], program_dept_wise_attainment['ProgramName']],
        y=program_dept_wise_attainment['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainment across Department Programs ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category descending'})
    fig.update_layout(height=600)
    return fig


def get_program_po_wise_attainment(filtered_df):
    pgm_po_wise_attain_df = filtered_df[
        ['ProgramId', 'ProgramName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL',
         'Target']].groupby(['ProgramId', 'ProgramName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
    metadata_df = filtered_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'POId', 'POName',
         'PODesc', 'SemesterId', 'SemesterName']].drop_duplicates()
    merged_df = pd.merge(pgm_po_wise_attain_df, metadata_df, how='inner',
                         left_on=['ProgramId', 'ProgramName', 'POId', 'POName'],
                         right_on=['ProgramId', 'ProgramName', 'POId', 'POName'])
    return merged_df


def get_program_po_wise_attainment_chart(pgm_po_wise_attain_df):
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDWOAL'],
        name='Direct Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDIWOAL'],
        name='Direct and Indirect Attainment without Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDWAL'],
        name='Direct Attainment with Affinity'
    ))
    fig.add_trace(go.Bar(
        x=[pgm_po_wise_attain_df['ProgramName'], pgm_po_wise_attain_df['POName']],
        y=pgm_po_wise_attain_df['POAttDIWAL'],
        name='Direct and Indirect Attainment with Affinity'
    ))
    fig.update_layout(
        title_text='Attainment ',
        xaxis_title_text='Attainments across Program level Program Objectives ',
        yaxis_title_text='Attainment %',
        barmode='group'  # or 'stack'
    )
    fig.update_layout(xaxis={'categoryorder': 'category ascending'})
    return fig


def get_powise_attainment(filtered_df):
    powise_po_attain_df = filtered_df[
        ['POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['POId', 'POName']).mean(numeric_only=True).reset_index()
    metadata_df = filtered_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'POId', 'POName',
         'PODesc']].drop_duplicates()
    merged_df = pd.merge(powise_po_attain_df, metadata_df, how='inner', left_on=['POId', 'POName'],
                         right_on=['POId', 'POName'])
    return merged_df


def get_powise_attainment_chart(powise_po_attain_df):
    fig = px.bar(powise_po_attain_df, x="POName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL'],
                 barmode='group')
    # Add target markers
    fig.add_trace(
        go.Scatter(x=powise_po_attain_df['POName'], y=powise_po_attain_df['Target'], mode='markers', name='Target'))
    fig.update_layout(xaxis={'categoryorder': 'total descending'})
    fig.update_layout(height=600)
    return fig


def analyze_using_ai(str):
    client = get_AI_client()
    response = client.chat.completions.create(
        messages=[
            {
                "role": "system",
                "content": "You are a helpful assistant.",
            },
            {
                "role": "user",
                "content": "The following shows the programs at the university that had the lowest attainment in the specified program outcomes. Use the descriptions and meanings of lowest performing program outcomes and make specific recommendations for each program with recommendations to improve the outcomes. Cover all programs and for each listed program, group recommended actions. :" + str,
            }
        ],
        max_tokens=4096,
        temperature=1.0,
        top_p=1.0,
        model="gpt-35-turbo-16k"
    )
    return response.choices[0].message.content


def get_course_po_wise_attainment(filtered_df):
    coursewise_po_attain_df = filtered_df[
        ['CourseId', 'CourseName', 'POId', 'POName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL',
         'Target']].groupby(
        ['CourseId', 'CourseName', 'POId', 'POName']).mean(numeric_only=True).reset_index()
    metadata_df = filtered_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'BatchId', 'BatchName',
         'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'POId', 'POName', 'PODesc']].drop_duplicates()
    merged_df = pd.merge(coursewise_po_attain_df, metadata_df, how='inner',
                         left_on=['CourseId', 'CourseName', 'POId', 'POName'],
                         right_on=['CourseId', 'CourseName', 'POId', 'POName'])
    return merged_df


def get_coursewise_po_attainment(filtered_df):
    coursewise_po_attain_df = filtered_df[
        ['CourseId', 'CourseName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['CourseId', 'CourseName']).mean(numeric_only=True).reset_index()
    metadata_df = filtered_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName', 'BatchId', 'BatchName',
         'SemesterId', 'SemesterName', 'CourseId', 'CourseName']].drop_duplicates()
    merged_df = pd.merge(coursewise_po_attain_df, metadata_df, how='inner', left_on=['CourseId', 'CourseName'],
                         right_on=['CourseId', 'CourseName'])
    return merged_df


def get_coursewise_po_attainment_chart(coursewise_po_attainment, failingOnly):
    if (failingOnly == True):
        fig = px.bar(
            coursewise_po_attainment[coursewise_po_attainment['POAttDWOAL'] <= coursewise_po_attainment['Target']],
            x="CourseName", y=['POAttDWOAL'], barmode='stack')
    else:
        fig = px.bar(coursewise_po_attainment, x="CourseName", y=['POAttDWOAL'], barmode='stack')
    # Add target markers
    fig.update_layout(xaxis={'categoryorder': 'total descending'})
    fig.update_layout(height=900)
    return fig


def get_filtered_dataset(dataset, master_school_id, selected_schools, selected_departments,
                         selected_programs):
    filtered_df = copo_attainment_df[dataset['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    if selected_departments:
        filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]
    if selected_programs:
        filtered_df = filtered_df[filtered_df['ProgramName'].isin(selected_programs)]

    return filtered_df


# --- END - Helper Functions ---
# --------------------------------

# outcomes_df = get_outcomes_from_csv("gu-podata.csv")
outcomes_df = get_outcomes_from_csv("pages/gu_and_cimr_po_data.csv")
outcomes_df = filter_outcomes_data(outcomes_df, 7)
copo_attainment_df = get_copo_attainment(outcomes_df)
# schoolwise_attainment = get_schoolwise_attainment(copo_attainment_df)
# schoolwise_attainment_fig = get_schoolwise_attainment_chart(schoolwise_attainment)
# pgmwise_attainment = get_program_wise_attainment(copo_attainment_df)
# pgmwise_attainment_fig = get_programwise_attainment_chart(pgmwise_attainment)
# program_batch_wise_attainment = get_program_batch_wise_attainment(copo_attainment_df)
# program_batch_wise_attainment_fig = get_program_batch_wise_attainment_chart(program_batch_wise_attainment)
# program_dept_wise_attainment = get_program_dept_wise_attainment(copo_attainment_df)
# program_dept_wise_attainment_fig = get_program_dept_wise_attainment_chart(program_dept_wise_attainment)
# dept_pgm_wise_attainment_fig = get_dept_pgm_wise_attainment_chart(program_dept_wise_attainment)

# pgm_po_wise_attain_df = get_program_po_wise_attainment(copo_attainment_df)
# pgmpo_wise_attainment_fig = get_program_po_wise_attainment_chart(pgmwise_attainment)
# powise_po_attain_df = get_powise_attainment(copo_attainment_df)
# powise_attain_fig = get_powise_attainment_chart(powise_po_attain_df)
# n_smallest_rows = powise_po_attain_df.nsmallest(20, 'POAttDWOAL')
# n_smallest_rows[['ProgramName', 'POName', 'PODesc', 'POAttDWOAL']].rename(inplace=True,
# columns={'ProgramName': 'Program Name',
#          'POName': 'Program Outcome',
#          'PODesc': 'Program Outcome Description',
#          'POAttDWOAL': 'Attainment'})
# json_string = n_smallest_rows.to_json(orient='records', lines=True)

# client = get_AI_client()
# analysis = analyze_using_ai(json_string)

# course_po_wise_attain_df = get_course_po_wise_attainment(copo_attainment_df)
# coursewise_po_attainment = get_coursewise_po_attainment(copo_attainment_df)
# course_po_attainment_fig_all = get_coursewise_po_attainment_chart(coursewise_po_attainment, False)
# course_po_attainment_fig_failing = get_coursewise_po_attainment_chart(coursewise_po_attainment, True)

# Get unique school names for the dropdown

layout = html.Div([
    html.H2("Program Outcomes Attainment Analysis"),
    html.Div([  # Main container for sidebar and content
        html.Div([  # Left sidebar
            html.H3("Filters"),
            html.H4("Select Schools"),
            dcc.Dropdown(
                id='school-filter-dropdown',
                options=[],  # Options will be populated by callback
                value=[],  # Default to None
                placeholder="Select Schools",
                multi=True,  # Allow multiple selections
                style={'width': '100%', 'margin-bottom': '20px'}
            ),
            html.H4("Select Departments"),
            dcc.Dropdown(
                id='department-filter-dropdown',
                options=[],  # Options will be populated by callback
                value=[],  # Default to None
                placeholder="Select Departments",
                multi=True,  # Allow multiple selections
                style={'width': '100%', 'margin-bottom': '20px'}
            ),
            html.H4("Select Programs"),
            dcc.Dropdown(
                id='program-filter-dropdown',
                options=[],  # Options will be populated by callback
                value=[],  # Default to None
                placeholder="Select Programs",
                multi=True,  # Allow multiple selections
                style={'width': '100%', 'margin-bottom': '10px'}
            )], style={'width': '23%', 'padding': '10px', 'background-color': '#f0f0f0', 'display': 'inline-block',                       
                       'position': 'fixed', 'height': '68vh', 'overflow-y': 'auto',
                       'vertical-align': 'top'}),  # Sidebar style
        html.Div([  # Main content area            
            html.H3("School-wise Program Attainment", style={'margin-top': '20px'}),
            html.Div(
                dcc.Graph(id='schoolwise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("Program-Department wise  Attainment"),
            html.Div(
                dcc.Graph(id='program_dept_wise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("Department wise Attainment across Programs"),
            html.Div(
                dcc.Graph(id='dept_pgm_wise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("Program wise Attainment"),
            html.Div(
                dcc.Graph(id='pgmwise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("Program-Batch wise  Attainment"),
            html.Div(
                dcc.Graph(id='program_batch_wise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("Program Related Outcomes wise Attainment"),
            html.Div(
                dcc.Graph(id='pgmpo_wise_attainment_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("PO (Program Outcome) wise Attainment"),
            html.Div(
                dcc.Graph(id='powise_attain_fig'),
                style={'display': 'inline-block', 'width': '100%'}
            ),
            html.H3("AI Analysis"),
            html.Div(id="ai-analysis-content"),
            html.H3("Course wise Program Outcomes Attainment"),
            html.Div([html.H4("Overall Attainment", style={'width': '48%', 'display': 'inline-block'}),
                      html.H4("Failing Courses", style={'width': '48%', 'display': 'inline-block'})],
                     style={'width': '100%', 'display': 'flex', 'justify-content': 'space-around',
                            'align-items': 'center'}),
            html.Div([
                dcc.Graph(id='course_po_attainment_fig_all', style={'width': '48%', 'display': 'inline-block'}),
                dcc.Graph(id='course_po_attainment_fig_failing', style={'width': '48%', 'display': 'inline-block'})
            ], style={'width': '100%', 'display': 'flex', 'justify-content': 'space-around'}),
        ], style={'width': '75%', 'display': 'inline-block', 'padding': '20px', 'margin-left': '25%',
                  'overflow-y': 'auto', 'height': '68vh'}),  # Main content style
    ], style={'width': '100%', 'display': 'flex'}),  # Main container style
])


# --- START - Dash Callbacks ---
# --------------------------------
@callback(
    Output('school-filter-dropdown', 'options'),
    Input('intermediate-master-school-id', 'data')
)
def update_school_dropdown_options(master_school_id):
    """Updates the school dropdown options based on the selected master school."""
    filtered_df = copo_attainment_df[copo_attainment_df['MasterSchoolId'] == master_school_id]
    available_schools = sorted(filtered_df['SchoolName'].unique())
    options = [{'label': school, 'value': school} for school in available_schools]
    return options


@callback(
    Output('department-filter-dropdown', 'options'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value')]
)
def update_department_dropdown_options(master_school_id, selected_schools):
    """Updates the department dropdown options based on the selected master school and schools."""
    filtered_df = copo_attainment_df[copo_attainment_df['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    available_departments = sorted(filtered_df['DepartmentName'].unique())
    options = [{'label': department, 'value': department} for department in available_departments]
    return options


@callback(
    Output('program-filter-dropdown', 'options'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value')]
)
def update_program_dropdown_options(master_school_id, selected_schools, selected_departments):
    """Updates the program dropdown options based on the selected master school, schools, and departments."""
    filtered_df = copo_attainment_df[copo_attainment_df['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    if selected_departments:
        filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]

    available_programs = sorted(filtered_df['ProgramName'].unique())

    options = [{'label': program, 'value': program} for program in available_programs]
    return options


@callback(
    Output('schoolwise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value')]
)
def update_schoolwise_attainment_fig(master_school_id, selected_schools):
    filtered_df = copo_attainment_df[copo_attainment_df['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    schoolwise_attainment = get_schoolwise_attainment(filtered_df)
    schoolwise_attainment_fig = get_schoolwise_attainment_chart(schoolwise_attainment)
    return schoolwise_attainment_fig


@callback(
    Output('pgmwise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_pgmwise_attainment_fig(master_school_id, selected_schools, selected_departments,
                                  selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    pgmwise_attainment = get_program_wise_attainment(filtered_df)
    pgmwise_attainment_fig = get_programwise_attainment_chart(pgmwise_attainment)
    return pgmwise_attainment_fig


@callback(
    Output('program_batch_wise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_program_batch_wise_attainment_fig(master_school_id, selected_schools, selected_departments,
                                             selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    program_batch_wise_attainment = get_program_batch_wise_attainment(filtered_df)
    program_batch_wise_attainment_fig = get_program_batch_wise_attainment_chart(program_batch_wise_attainment)
    return program_batch_wise_attainment_fig


@callback(
    Output('program_dept_wise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_program_dept_wise_attainment_fig(master_school_id, selected_schools, selected_departments,
                                            selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    program_dept_wise_attainment = get_program_dept_wise_attainment(filtered_df)
    program_dept_wise_attainment_fig = get_program_dept_wise_attainment_chart(program_dept_wise_attainment)
    return program_dept_wise_attainment_fig


@callback(
    Output('dept_pgm_wise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_dept_pgm_wise_attainment_fig(master_school_id, selected_schools, selected_departments, selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    program_dept_wise_attainment = get_program_dept_wise_attainment(filtered_df)
    dept_pgm_wise_attainment_fig = get_dept_pgm_wise_attainment_chart(program_dept_wise_attainment)
    return dept_pgm_wise_attainment_fig


@callback(
    Output('pgmpo_wise_attainment_fig', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_pgmpo_wise_attainment_fig(master_school_id, selected_schools, selected_departments, selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    pgm_po_wise_attain_df = get_program_po_wise_attainment(filtered_df)
    pgmpo_wise_attainment_fig = get_program_po_wise_attainment_chart(pgm_po_wise_attain_df)
    return pgmpo_wise_attainment_fig


@callback(
    Output('powise_attain_fig', 'figure'), [
        Input('intermediate-master-school-id', 'data'),
        Input('school-filter-dropdown', 'value'),
        Input('department-filter-dropdown', 'value'),
        Input('program-filter-dropdown', 'value')]
)
def update_powise_attain_fig(master_school_id, selected_schools, selected_departments, selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    powise_po_attain_df = get_powise_attainment(filtered_df)
    powise_attain_fig = get_powise_attainment_chart(powise_po_attain_df)
    return powise_attain_fig


@callback(
    Output('course_po_attainment_fig_all', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_course_po_attainment_fig_all(master_school_id, selected_schools, selected_departments, selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    coursewise_po_attainment = get_coursewise_po_attainment(filtered_df)
    course_po_attainment_fig_all = get_coursewise_po_attainment_chart(coursewise_po_attainment, False)
    return course_po_attainment_fig_all


@callback(
    Output('course_po_attainment_fig_failing', 'figure'),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]
)
def update_course_po_attainment_fig_failing(master_school_id, selected_schools, selected_departments,
                                            selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)
    coursewise_po_attainment = get_coursewise_po_attainment(filtered_df)
    course_po_attainment_fig_failing = get_coursewise_po_attainment_chart(coursewise_po_attainment, True)
    return course_po_attainment_fig_failing


@callback(
    Output("ai-analysis-content", "children"),
    [Input('intermediate-master-school-id', 'data'),
     Input('school-filter-dropdown', 'value'),
     Input('department-filter-dropdown', 'value'),
     Input('program-filter-dropdown', 'value')]  # Assuming this comes from app.py
)
def update_ai_analysis(master_school_id, selected_schools, selected_departments,
                       selected_programs):
    filtered_df = get_filtered_dataset(copo_attainment_df, master_school_id, selected_schools,
                                       selected_departments, selected_programs)

    powise_po_attain_df = get_powise_attainment(filtered_df)
    n_smallest_rows = powise_po_attain_df.nsmallest(20, 'POAttDWOAL')
    n_smallest_rows[['ProgramName', 'POName', 'PODesc', 'POAttDWOAL']].rename(
        inplace=True,
        columns={'ProgramName': 'Program Name', 'POName': 'Program Outcome', 'PODesc': 'Program Outcome Description',
                 'POAttDWOAL': 'Attainment'})
    json_string = n_smallest_rows.to_json(orient='records', lines=True)
    analysis = analyze_using_ai(json_string)
    return html.P(analysis)  # Update the content dynamically
