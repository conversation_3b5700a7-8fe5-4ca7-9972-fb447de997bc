import pyodbc
import pandas as pd


class DatabaseConnector:
    def __init__(self, server, database, username, password):
        """
        Initializes the DatabaseConnector with connection details.

        Args:
            server (str): The SQL Server instance name or IP address.
            database (str): The name of the database.
            username (str): The database username.
            password (str): The database password.
        """
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.connection_string = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"  # Or your specific driver
            f"SERVER={self.server};"
            f"DATABASE={self.database};"
            f"UID={self.username};"
            f"PWD={self.password};"
        )
        self.conn = None

    def connect(self):
        """
        Establishes a connection to the SQL Server database.
        """
        try:
            self.conn = pyodbc.connect(self.connection_string)
            print("Connected to SQL Server successfully!")
        except pyodbc.Error as ex:
            sqlstate = ex.args[0]
            print(f"Error connecting to SQL Server: {sqlstate}")
            raise

    def disconnect(self):
        """
        Closes the connection to the SQL Server database.
        """
        if self.conn:
            self.conn.close()
            print("Disconnected from SQL Server.")

    def execute_query(self, query):
        """
        Executes a SELECT query and returns the results as a Pandas DataFrame.

        Args:
            query (str): The SQL SELECT query to execute.

        Returns:
            pandas.DataFrame: The query results as a DataFrame, or None if an error occurs.
        """
        if not self.conn:
            raise Exception("Not connected to the database. Call connect() first.")

        try:
            df = pd.read_sql(query, self.conn)
            return df
        except pyodbc.Error as ex:
            sqlstate = ex.args[0]
            print(f"Error executing query: {sqlstate}")
            return None
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return None

# Example usage (you can remove this part in your final version):
# if __name__ == "__main__":
#     # Replace with your actual credentials
#     db_server = "your_server_name"
#     db_name = "your_database_name"
#     db_user = "your_username"
#     db_password = "your_password"
#
#     db_connector = DatabaseConnector(db_server, db_name, db_user, db_password)
#     try:
#         db_connector.connect()
#
#         # Example query
#         co_query = "SELECT * FROM GU_CO_attainment"
#         co_df = db_connector.execute_query(co_query)
#         if co_df is not None:
#             print("CO Data:")
#             print(co_df.head())
#
#         po_query = "SELECT * FROM GU_PO_attainment"
#         po_df = db_connector.execute_query(po_query)
#         if po_df is not None:
#             print("PO Data:")
#             print(po_df.head())
#
#     except Exception as e:
#         print(f"An error occurred: {e}")
#     finally:
#         db_connector.disconnect()
