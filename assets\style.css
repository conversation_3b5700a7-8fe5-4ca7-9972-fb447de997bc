html, body, #_dash-app-content, #react-entry-point {
    height: 100%; /* Important for Dash app to fill height */
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent extra scrollbars from initial layout */
}

#main-content {
    height: 100vh; /* Ensure main content area also uses viewport height */
    overflow-y: auto; /* Allow scrolling within main content */
}

#sidebar {
    height: 100vh; /* Ensure sidebar uses viewport height */
    overflow: hidden; /* Sidebar itself won't scroll, its content will */
}

/* Adjust the toggle button position slightly when sidebar is collapsed */
#sidebar-toggle-button.collapsed {
    left: 0px; /* Adjust as needed */
    right: auto;
}