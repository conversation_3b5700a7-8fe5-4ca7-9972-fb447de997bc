import dash
from dash import Dash, html, dcc, Input, Output, callback, dash_table
import dash_core_components as dcc
import dash_html_components as html
from dash.dependencies import Input, Output
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from flask_login import current_user, logout_user
from dash.exceptions import PreventUpdate
from db_connector import DatabaseConnector

# Define styles
header_style = {
    'display': 'flex',
    'justify-content': 'space-between',
    'align-items': 'center',
    'padding': '10px 20px',
    'background-color': '#2c3e50',
    'color': 'white',
    'margin-bottom': '20px'
}

title_style = {
    'margin': '0',
    'font-size': '24px'
}

user_info_style = {
    'display': 'flex',
    'align-items': 'center',
    'gap': '15px'
}

logout_button_style = {
    'background-color': '#e74c3c',
    'color': 'white',
    'border': 'none',
    'padding': '8px 15px',
    'border-radius': '4px',
    'cursor': 'pointer'
}

# --- Variables ---
co_df = None
use_csv = True


# --- START - Helper Functions ---
# --------------------------------
def execute_sql_query(query):
    # --- Database Connection ---
    # Replace with your actual credentials
    DB_SERVER = "20.242.124.131"
    DB_NAME = "galgotiasuni_prodDB"
    DB_USER = "datafiReader"
    DB_PASSWORD = "P96Bry3rnGAv#9z8&f19tgS5reX4MU&T"

    db_connector = DatabaseConnector(DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD)
    try:
        db_connector.connect()
        # --- Data Loading and Preprocessing ---
        # co_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 2"
        result = db_connector.execute_query(query)
        result = result.rename(columns={'Attainmemt': 'Attainment'})
        if result is None:
            raise Exception("Failed to load CO data from database.")
        else:
            return result

    except Exception as e:
        print(f"Error during database operations: {e}")


def get_programwise_attainment_chart(pgmwise_attainment):
    fig = px.bar(pgmwise_attainment, x="ProgramName", y=['POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', ],
                 barmode='group', template='seaborn')
    # Add target markers
    # fig.update_layout(xaxis={'categoryorder':'total descending'})
    fig.update_layout(height=600)
    return fig


def get_semesterwise_attainment(merged_df):
    semesterwise_attainment = merged_df[
        ['SemesterId', 'SemesterName', 'POAttDWOAL', 'POAttDIWOAL', 'POAttDWAL', 'POAttDIWAL', 'Target']].groupby(
        ['SemesterId', 'SemesterName']).mean(numeric_only=True).reset_index()
    return semesterwise_attainment


def get_semesterwise_attainment_chart(build_level2_df_semester):
    fig = px.bar(build_level2_df_semester, x="SemesterName", y=["Attained", 'NotAttained'])
    return fig


def build_level2_df(df):
    temp_df = df[df['MasterSchoolId'] == 294]
    level2_df = temp_df.groupby(
        ['SchoolId', 'SchoolName', 'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'COName']).mean(
        numeric_only=True).reset_index()
    level2_df['Attained'] = level2_df['Attainment'] >= level2_df['Threshold']
    level2_df['NotAttained'] = level2_df['Attainment'] < level2_df['Threshold']
    level2_df = level2_df[['SchoolId', 'SchoolName', 'SemesterId', 'SemesterName', 'Attained', 'NotAttained']].groupby(
        ['SchoolId', 'SchoolName']).sum().reset_index()
    return level2_df


def build_level2_df_semester(df):
    temp_df = df[df['MasterSchoolId'] == 294]
    level2_df = temp_df.groupby(
        ['SchoolId', 'SchoolName', 'SemesterId', 'SemesterName', 'CourseId', 'CourseName', 'COName']).mean(
        numeric_only=True).reset_index()
    level2_df['Attained'] = level2_df['Attainment'] >= level2_df['Threshold']
    level2_df['NotAttained'] = level2_df['Attainment'] < level2_df['Threshold']
    level2_df = level2_df[['SchoolId', 'SchoolName', 'SemesterId', 'SemesterName', 'Attained', 'NotAttained']].groupby(
        ['SemesterId', 'SemesterName']).sum().reset_index()
    return level2_df


def get_schoolwise_attainment_chart(level2_df):
    fig = px.bar(level2_df, x="SchoolName", y=["Attained", 'NotAttained'])
    school_names = level2_df['SchoolName']
    return fig


def get_schoolwise_attainment_100chart(level2_df):
    level2_df_copy = level2_df.copy()
    level2_df_copy['Attained_pct'] = level2_df_copy['Attained'] / (
            level2_df_copy['Attained'] + level2_df_copy['NotAttained']) * 100
    level2_df_copy['NotAttained_pct'] = level2_df_copy['NotAttained'] / (
            level2_df_copy['Attained'] + level2_df_copy['NotAttained']) * 100
    fig = px.bar(level2_df_copy, x="SchoolName", y=["Attained_pct", 'NotAttained_pct'])
    return fig


def get_schoolwise_attainment_distr(df):
    filtered_df = df[df['MasterSchoolId'] == 294]
    avg_attainment_df = filtered_df.groupby(['SchoolId', 'SchoolName', 'CourseId', 'CourseName']).mean(
        numeric_only=True).reset_index()
    # Define bins and labels
    print(avg_attainment_df)
    bins = [0, 20, 40, 60, 80, 100]
    labels = ['0-20', '21-40', '41-60', '61-80', '81-100']
    # Create buckets using pd.cut()
    avg_attainment_df['bucket'] = pd.cut(avg_attainment_df['Attainment'], bins=bins, labels=labels, right=False)
    attainment_buckets_df = avg_attainment_df[['SchoolId', 'SchoolName', 'bucket']]
    attainment_buckets_df['count'] = 1
    schoolwise_buckets_distr_df = attainment_buckets_df.groupby(['SchoolId', 'SchoolName', 'bucket'],
                                                                observed=True).count().reset_index()
    schoolwise_buckets_distr_df = schoolwise_buckets_distr_df.pivot_table('count', ['SchoolName'], 'bucket').fillna(0)
    return schoolwise_buckets_distr_df


def get_programwise_attainment(univ_df):
    sectionwise_df = univ_df[univ_df['MasterSchoolId'] == 294]
    pgm_metadata_df = sectionwise_df[
        ['SchoolId', 'SchoolName', 'DepartmentId', 'DepartmentName', 'ProgramId', 'ProgramName']].drop_duplicates()
    pgm_co_df = sectionwise_df[
        ['ProgramId', 'ProgramName', 'COId', 'COName', 'Attainment', 'Threshold']].drop_duplicates()
    pgm_co_attn_df = pgm_co_df.groupby(['ProgramId', 'ProgramName', 'COId', 'COName']).sum().reset_index()
    pgm_co_attn_df['Attained'] = (pgm_co_attn_df['Attainment'] >= pgm_co_attn_df['Threshold']) * 1
    pgm_co_attn_df['NotAttained'] = (pgm_co_attn_df['Attainment'] < pgm_co_attn_df['Threshold']) * 1
    aggr_df = pgm_co_attn_df[['ProgramId', 'ProgramName', 'Attained', 'NotAttained']].groupby(
        ['ProgramId', 'ProgramName']).sum().reset_index()
    aggr_df['AttainedPct'] = aggr_df['Attained'] * 100.0 / (aggr_df['Attained'] + aggr_df['NotAttained'])
    aggr_df['NotAttainedPct'] = aggr_df['NotAttained'] * 100.0 / (aggr_df['Attained'] + aggr_df['NotAttained'])
    merged_df = pd.merge(aggr_df, pgm_metadata_df, left_on=['ProgramId', 'ProgramName'],
                         right_on=['ProgramId', 'ProgramName'], how='inner')
    # fig = px.bar(merged_df, x='ProgramName', y=['Attained','NotAttained'], width = 600, height=800, title='Program Attainment')
    # fig.show()
    # fig = px.bar(merged_df, x='ProgramName', y=['AttainedPct','NotAttainedPct'], width = 600, height=800, title='Program Attainment Pct')
    # fig.show()
    print(merged_df[['ProgramName', 'Attained', 'NotAttained', 'AttainedPct', 'NotAttainedPct']])
    return merged_df


def get_programwise_attainment_chart(pgmwise_attainment_df):
    # execute_sql_query()
    fig = px.bar(pgmwise_attainment_df, x="ProgramName", y=["Attained", 'NotAttained'])
    return fig


def get_programwise_attainment_pct_chart(pgmwise_attainment_df):
    fig = px.bar(pgmwise_attainment_df, x="ProgramName", y=["AttainedPct", 'NotAttainedPct'])
    return fig


def get_filtered_dataset(dataset, master_school_id, selected_schools, selected_departments,
                         selected_programs):
    filtered_df = dataset[dataset['MasterSchoolId'] == master_school_id]
    if selected_schools:
        filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
    if selected_departments:
        filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]
    if selected_programs:
        filtered_df = filtered_df[filtered_df['ProgramName'].isin(selected_programs)]
    return filtered_df


def get_filtered_courses(dataset, master_school_id, selected_schools, selected_departments,
                         selected_programs):
    filtered_df = get_filtered_dataset(dataset, master_school_id, selected_schools, selected_departments,
                                       selected_programs)
    return filtered_df[['CourseName', 'CourseId']].drop_duplicates()


def get_top_programs_attained(df):
    """Calculates the number of attained COs for each program."""
    # Ensure 'Attained' column is boolean
    df['Attained'] = df['Attained'].astype(bool)

    # Group by program and sum the attained COs
    program_attainment = df.groupby('ProgramName')['Attained'].sum().reset_index()
    program_attainment.rename(columns={'Attained': 'COs Attained'}, inplace=True)

    # Sort by COs Attained in descending order and get the top 10
    top_10_programs = program_attainment.nlargest(10, 'COs Attained')
    return top_10_programs


def get_bottom_programs_attained(df):
    """Calculates the number of attained COs for each program."""
    # Ensure 'Attained' column is boolean
    df['Attained'] = df['Attained'].astype(bool)

    # Group by program and sum the attained COs
    program_attainment = df.groupby('ProgramName')['Attained'].sum().reset_index()
    program_attainment.rename(columns={'Attained': 'COs Attained'}, inplace=True)
    # Sort by COs Attained in ascending order and get the top 10
    bottom_10_programs = program_attainment.nsmallest(10, 'COs Attained')
    return bottom_10_programs


def get_top_programs_chart(top_10_programs):
    """Creates a bar chart of the top 10 programs with the most attained COs."""
    fig = px.bar(top_10_programs,
                 x='ProgramName',
                 y='COs Attained',
                 title='Top 10 Programs with Highest CO Attainment Count',
                 labels={'ProgramName': 'Program Name', 'COs Attained': 'Number of COs Attained'},
                 template='seaborn')  # Use seaborn template for better aesthetics
    fig.update_layout(xaxis_title="Program Name", yaxis_title="Number of COs Attained")
    return fig


def create_bottom_programs_chart(bottom_10_programs):
    """Creates a bar chart of the bottom 10 programs with the least attained COs."""
    fig = px.bar(bottom_10_programs,
                 x='ProgramName',
                 y='COs Attained',
                 title='Top 10 Programs with Lowest CO Attainment Count',
                 labels={'ProgramName': 'Program Name', 'COs Attained': 'Number of COs Attained'},
                 template='seaborn')  # Use seaborn template for better aesthetics
    fig.update_layout(xaxis_title="Program Name", yaxis_title="Number of COs Attained")
    return fig


# --- END - Helper Functions ---
# --------------------------------


def create_gu_app(server):
    # CO related code
    # --- Data Preprocessing ---

    # --- Data Loading ---
    if use_csv:
        try:
            co_df = pd.read_csv('pages/gu-course-codata.csv')
            co_df.rename(columns={'School': 'SchoolName', 'Department': 'DepartmentName',
                                  'Program': 'ProgramName', 'Batch': 'BatchName', 'Semester': 'SemesterName',
                                  'Course': 'CourseName'}, inplace=True)
        except FileNotFoundError as e:
            co_df = pd.DataFrame()
            print(f"Error: One or more data files not found: {e}")

    else:
        # --- Database Connection ---
        # Replace with your actual credentials
        DB_SERVER = "20.242.124.131"
        DB_NAME = "galgotiasuni_prodDB"
        DB_USER = "datafiReader"
        DB_PASSWORD = "P96Bry3rnGAv#9z8&f19tgS5reX4MU&T"

        db_connector = DatabaseConnector(DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD)
        try:
            db_connector.connect()
            # --- Data Loading and Preprocessing ---
            co_query = "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas where COAttainmentLevel = 2"
            co_df = db_connector.execute_query(co_query)
            co_df = co_df.rename(columns={'Attainmemt': 'Attainment'})
            if co_df is None:
                raise Exception("Failed to load CO data from database.")

        except Exception as e:
            print(f"Error during database operations: {e}")

    courses_df = co_df[['CourseName', 'CourseId']].drop_duplicates()
    # Convert DataFrame columns into dropdown options
    course_options = [
        {'label': row['CourseName'], 'value': row['CourseId']} for _, row in courses_df.iterrows()
    ]

    # Dimension columns
    dim_sectionId = 'CourseId'
    dim_coname = 'COName'
    dim_assgn = 'AssignmentName'
    # Measure column
    measure_col = 'Attainment'
    colors = ['#636EFA', '#EF553B', '#00CC96', '#AB63FA', '#FFA15A', '#19D3F3', '#FF6692', '#B6E880', '#FF97FF',
              '#FECB52']

    available_schools = sorted(co_df['SchoolName'].unique())
    school_options = [{'label': school, 'value': school} for school in available_schools]

    try:
        master_school_df_raw = pd.read_csv('pages/master-schools.csv')
    except FileNotFoundError as e:
        print(f"Error: One or more data files not found: {e}")

    master_school_df = master_school_df_raw[['MasterSchoolName', 'MasterSchoolId']].drop_duplicates()
    master_school_options = [
        {'label': row['MasterSchoolName'], 'value': row['MasterSchoolId']} for _, row in master_school_df.iterrows()
    ]

    # Create Dash app
    gu_app = dash.Dash(__name__, server=server, url_base_pathname='/dashboard/')
    gu_app.config.suppress_callback_exceptions = True

    # Sample data for the plot
    df = pd.DataFrame({
        'x': range(10),
        'y': [i ** 2 for i in range(10)]
    })

    # Define the dashboard layout
    gu_app.layout = html.Div([
        dcc.Location(id='url', refresh=True),
        # Header with user info and logout button
        html.Div([
            html.H1('Course Outcomes Attainment Analysis', style=title_style),
            html.Div(id='user-info', style=user_info_style)
        ], style=header_style),
        # Main content -- Sample chart
        # html.Div([
        #     dcc.Graph(
        #         id='example-graph',
        #         figure=px.scatter(df, x='x', y='y', title='Sample Plot - Linear to Square')
        #     )
        # ], style={'padding': '0 20px'}),

        html.Div([
            html.Div([
                dcc.Dropdown(
                    id='ip-master-school-id',
                    value=294,
                    options=master_school_options,
                )
            ], style={'width': '48%', 'display': 'none'}),
            # html.Div(id='op-master-school-id'),
            dcc.Store(id='intermediate-master-school-id', data=[], storage_type='memory'),

            html.Div([
                html.Div([  # Left sidebar

                    html.H3("Filters"),
                    html.H4("Select Schools"),
                    dcc.Dropdown(
                        id='school-filter-dropdown-co',
                        options=school_options,  # Options will be populated by callback
                        value=[],  # Default to None
                        placeholder="Select Schools",
                        multi=True,  # Allow multiple selections
                        style={'width': '100%', 'margin-bottom': '20px'}
                    ),
                    html.H4("Select Departments"),
                    dcc.Dropdown(
                        id='department-filter-dropdown-co',
                        options=[],  # Options will be populated by callback
                        value=[],  # Default to None
                        placeholder="Select Departments",
                        multi=True,  # Allow multiple selections
                        style={'width': '100%', 'margin-bottom': '20px'}
                    ),
                    html.H4("Select Programs"),
                    dcc.Dropdown(
                        id='program-filter-dropdown-co',
                        options=[],  # Options will be populated by callback
                        value=[],  # Default to None
                        placeholder="Select Programs",
                        multi=True,  # Allow multiple selections
                        style={'width': '100%', 'margin-bottom': '10px'}
                    )],
                    style={'width': '23%', 'padding': '10px', 'background-color': '#f0f0f0', 'display': 'inline-block',
                           'position': 'fixed', 'height': '84vh', 'overflow-y': 'auto',
                           'vertical-align': 'top'}),  # Sidebar style
                html.Div([  # Main content area
                    html.H3("School-wise Attainment"),
                    html.Div(
                        children=[
                            html.Div(
                                dcc.Graph(id='school-wise-attainment-chart'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),
                            html.Div(
                                dcc.Graph(id='school-wise-attainment-pct-chart'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),
                        ],
                        style={'width': '100%', 'display': 'inline-block'}
                    ),
                    html.H3("Semester-wise Attainment"),
                    html.Div(
                        children=[
                            dcc.Graph(id='semester-wise-attainment-chart'),
                        ],
                        style={'width': '100%', 'display': 'inline-block'}
                    ),
                    html.H3("Top 10 Programs by CO Attainment Count"),
                    html.Div(
                        children=[
                            html.Div(
                                dcc.Graph(id='top-10-programs-highest-co'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),
                            html.Div(
                                dcc.Graph(id='top-10-programs-lowest-co'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),

                        ],
                        style={'width': '100%', 'display': 'inline-block'}
                    ),

                    html.H3("Program-wise CO Attainment"),
                    html.Div(
                        children=[
                            html.Div(
                                dcc.Graph(id='pgmwise-attainment-chart'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),
                            html.Div(
                                dcc.Graph(id='pgmwise-attainment-pct-chart'),
                                style={'display': 'inline-block', 'width': '48%'}
                            ),
                        ],
                        style={'width': '100%', 'display': 'inline-block'}
                    ),
                    html.H3("Count of Courses Distribution in Ranges – COs Attainment % wise"),
                    dash_table.DataTable(
                        id='schoolwise-co-attainment-table'),
                    html.H3("Courses:"),
                    dcc.Dropdown(
                        id='course-selector',
                        options=course_options,
                        value=1  # Default value
                    ),
                    dcc.Graph(
                        id='course-bar-chart'
                    ),
                    # dcc.Graph(
                    #     id='course-assgn-bar-chart'
                    # ),
                    # dcc.Graph(
                    #     id='course-co-bar-chart'
                    # ),
                    # dcc.Graph(
                    #     id='sect-assgn-co-attainment-chart'
                    # ),
                    # dcc.Graph(
                    #     id='sect-student-co-attainment-chart'
                    # )
                ], style={'width': '75%', 'display': 'inline-block', 'padding': '10px', 'margin-left': '25%',
                          'overflow-y': 'auto', 'height': '84vh'}),
            ], style={'width': '100%', 'display': 'flex'}),
        ])

    ])

    # Callback to display user info
    @gu_app.callback(
        Output('user-info', 'children'),
        Input('user-info', 'id')
    )
    def update_user_info(_):
        if current_user.is_authenticated:
            return [
                html.Div([
                    html.Span(f'Welcome, {current_user.first_name} {current_user.last_name}'),
                    html.Div(f'{current_user.id}', style={'font-size': '12px'})
                ]),
                html.Button('Logout', id='logout-button', n_clicks=0, style=logout_button_style)
            ]
        return html.Div([
            html.H3('You are not logged in.')
        ])

    # Callback for logout button
    @gu_app.callback(
        Output('url', 'pathname'),
        Input('logout-button', 'n_clicks'),
        prevent_initial_call=True
    )
    def logout(n_clicks):
        if n_clicks > 0:
            logout_user()
            return '/login'
        raise PreventUpdate

    # --- START - Dash Callbacks for left pane dropdowns ---
    # --------------------------------

    @gu_app.callback(
        Output('intermediate-master-school-id', 'data'),
        Input('ip-master-school-id', 'value'))
    def store_master_school_id(master_school_id):
        return master_school_id

    @gu_app.callback(
        Output('school-filter-dropdown-co', 'options'),
        Input('intermediate-master-school-id', 'data')
    )
    def update_school_dropdown_options(master_school_id):
        """Updates the school dropdown options based on the selected master school."""
        filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
        available_schools = sorted(filtered_df['SchoolName'].unique())
        options = [{'label': school, 'value': school} for school in available_schools]
        return options

    @gu_app.callback(
        Output('department-filter-dropdown-co', 'options'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value')]
    )
    def update_department_dropdown_options(master_school_id, selected_schools):
        """Updates the department dropdown options based on the selected master school and schools."""
        filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
        if selected_schools:
            filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
        available_departments = sorted(filtered_df['DepartmentName'].unique())
        options = [{'label': department, 'value': department} for department in available_departments]
        return options

    @gu_app.callback(
        Output('program-filter-dropdown-co', 'options'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value')]
    )
    def update_program_dropdown_options(master_school_id, selected_schools, selected_departments):
        """Updates the program dropdown options based on the selected master school, schools, and departments."""
        filtered_df = co_df[co_df['MasterSchoolId'] == master_school_id]
        if selected_schools:
            filtered_df = filtered_df[filtered_df['SchoolName'].isin(selected_schools)]
        if selected_departments:
            filtered_df = filtered_df[filtered_df['DepartmentName'].isin(selected_departments)]

        available_programs = sorted(filtered_df['ProgramName'].unique())
        options = [{'label': program, 'value': program} for program in available_programs]
        return options

    # --- END - Dash Callbacks for left pane dropdowns ---
    # --------------------------------

    @gu_app.callback(
        Output('top-10-programs-highest-co', 'figure'),
        [Input('intermediate-master-school-id', 'data'),  # Or your filter inputs
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_top_programs_chart(master_school_id, selected_schools, selected_departments, selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df is None:
                raise Exception("Data not available.")
            top_10 = get_top_programs_attained(filtered_df)
            return get_top_programs_chart(top_10)
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    @gu_app.callback(
        Output('top-10-programs-lowest-co', 'figure'),
        [Input('intermediate-master-school-id', 'data'),  # Or your filter inputs
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_bottom_programs_chart(master_school_id, selected_schools, selected_departments, selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                return go.Figure()  # Return empty figure if no data
            bottom_10 = get_bottom_programs_attained(filtered_df)
            return create_bottom_programs_chart(bottom_10)
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the course-selector dropdown ---
    @gu_app.callback(
        Output('course-selector', 'options'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_course_selector_options(master_school_id, selected_schools, selected_departments, selected_programs):
        try:
            filtered_df = get_filtered_courses(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df is None:
                raise Exception("Data not available.")
            courses_df_filtered = filtered_df[['CourseName', 'CourseId']].drop_duplicates()
            course_options = [
                {'label': row['CourseName'], 'value': row['CourseId']} for _, row in courses_df_filtered.iterrows()
            ]
            return course_options
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return []  # Return empty options in case of error

    # --- Start - Dash Callbacks for charts ---
    # --------------------------------
    # --- Callback to update the program-wise attainment chart ---
    @gu_app.callback(
        Output('pgmwise-attainment-chart', 'figure'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_pgmwise_attainment_chart(master_school_id, selected_schools, selected_departments, selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                raise Exception("Data not available.")

            pgmwise_attainment_df = get_programwise_attainment(filtered_df)
            fig = get_programwise_attainment_chart(pgmwise_attainment_df)
            return fig
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the program wise attainment pct chart ---
    @gu_app.callback(
        Output('pgmwise-attainment-pct-chart', 'figure'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_pgmwise_pct_attainment_chart(master_school_id, selected_schools, selected_departments,
                                            selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                raise Exception("Data not available.")

            pgmwise_attainment_df = get_programwise_attainment(filtered_df)
            fig = get_programwise_attainment_pct_chart(pgmwise_attainment_df)
            return fig
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the school wise attainment chart ---
    @gu_app.callback(
        Output('school-wise-attainment-chart', 'figure'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_school_wise_attainment_chart(master_school_id, selected_schools, selected_departments,
                                            selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                raise Exception("Data not available.")

            level2_df = build_level2_df(filtered_df)
            fig = get_schoolwise_attainment_chart(level2_df)
            return fig
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the semester wise attainment chart ---
    @gu_app.callback(
        Output('semester-wise-attainment-chart', 'figure'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_semester_wise_attainment_chart(master_school_id, selected_schools, selected_departments,
                                              selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                raise Exception("Data not available.")
            level2_df = build_level2_df_semester(filtered_df)
            fig = get_semesterwise_attainment_chart(level2_df)
            return fig
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the school wise attainment pct chart ---
    @gu_app.callback(
        Output('school-wise-attainment-pct-chart', 'figure'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_school_wise_attainment_pct_chart(master_school_id, selected_schools, selected_departments,
                                                selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df.empty:
                raise Exception("Data not available.")

            level2_df = build_level2_df(filtered_df)
            fig = get_schoolwise_attainment_100chart(level2_df)
            return fig
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return {}  # Return an empty figure in case of error

    # --- Callback to update the school-wise-co-attainment-table ---
    @gu_app.callback(
        Output('schoolwise-co-attainment-table', 'columns'),
        Output('schoolwise-co-attainment-table', 'data'),
        [Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_schoolwise_co_attainment_table(master_school_id, selected_schools, selected_departments,
                                              selected_programs):
        try:
            filtered_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                               selected_programs)
            if filtered_df is None:
                raise Exception("Data not available.")
            schoolwise_distr_df = get_schoolwise_attainment_distr(filtered_df).reset_index()
            columns = [{"name": i, "id": i} for i in schoolwise_distr_df.columns]
            data = schoolwise_distr_df.to_dict('records')
            return columns, data
        except Exception as e:
            print(f"Error during data processing operations: {e}")
            return [], []  # Return empty columns and data in case of error

    # Callback to update the main bar chart based on dropdown selection
    @gu_app.callback(
        Output('course-bar-chart', 'figure'),
        [Input('course-selector', 'value'),
         Input('intermediate-master-school-id', 'data'),
         Input('school-filter-dropdown-co', 'value'),
         Input('department-filter-dropdown-co', 'value'),
         Input('program-filter-dropdown-co', 'value')]
    )
    def update_course_chart(selected_group, master_school_id, selected_schools, selected_departments,
                            selected_programs):
        if selected_group == 'no_chart':
            raise PreventUpdate
            return {  # Return empty figure if 'No Chart' is selected
                "layout": {
                    "xaxis": {"visible": False},
                    "yaxis": {"visible": False},
                    "annotations": [
                        {
                            "text": str(selected_group),
                            "xref": "paper",
                            "yref": "paper",
                            "showarrow": False,
                            "font": {"size": 20}
                        }
                    ]
                }
            }
        elif selected_group in courses_df['CourseId'].values:
            if not use_csv:
                co_query = (
                        "SELECT TOP (25000) * FROM dbo.COAttainmentComputedMetaDatas WHERE COAttainmentLevel = 2 AND MasterSchoolId = "
                        + str(master_school_id) + " AND CourseId = " + str(selected_group))
                result = execute_sql_query(co_query)
                course_df = result
                # course_df = result[(result['CourseId'] == selected_group) & (result['COAttainmentLevel'] == 2)]
            else:
                course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
                                                 selected_programs)
                course_df = course_df[(course_df['CourseId'] == selected_group)]

            fig = go.Figure()
            color_index = 0
            subcat_co = course_df[dim_coname].unique()

            for subcat in subcat_co:
                filtered_df = course_df[course_df[dim_coname] == subcat]
                fig.add_trace(go.Bar(
                    x=filtered_df[dim_sectionId],
                    y=filtered_df[measure_col],
                    name=subcat,
                    marker_color=colors[color_index % len(colors)]
                ))
                color_index += 1

            fig.update_layout(
                barmode='group',
                title='Attainment by Sections and Course Outcomes',
                xaxis_title=dim_sectionId,
                yaxis_title=measure_col,
                legend_title=dim_coname
            )
            return fig
        else:
            return go.Figure()  # Return empty figure if group not found (shouldn't happen with our data)

    # @gu_app.callback(
    #     Output('course-assgn-bar-chart', 'figure'),
    #     [Input('course-selector', 'value'),
    #      Input('intermediate-master-school-id', 'data'),
    #      Input('school-filter-dropdown-co', 'value'),
    #      Input('department-filter-dropdown-co', 'value'),
    #      Input('program-filter-dropdown-co', 'value')]
    # )
    # def update_course_assgn_chart(selected_group, master_school_id, selected_schools, selected_departments,
    #                               selected_programs):
    #     if selected_group == 'no_chart':
    #         raise PreventUpdate
    #         return {  # Return empty figure if 'No Chart' is selected
    #             "layout": {
    #                 "xaxis": {"visible": False},
    #                 "yaxis": {"visible": False},
    #                 "annotations": [
    #                     {
    #                         "text": str(selected_group),
    #                         "xref": "paper",
    #                         "yref": "paper",
    #                         "showarrow": False,
    #                         "font": {"size": 20}
    #                     }
    #                 ]
    #             }
    #         }
    #     # elif selected_group in courses_df['CourseId'].values:
    #     else:
    #         course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
    #                                          selected_programs)
    #         course_df = course_df[(course_df['CourseId'] == selected_group) & (course_df['COAttainmentLevel'] == 0)]
    #         subcat_assgn = course_df[dim_assgn].unique()
    #         print(subcat_assgn)
    #         fig = go.Figure()
    #         color_index = 0
    #         for subcat in subcat_assgn:
    #             filtered_df = course_df[course_df[dim_assgn] == subcat]
    #             val = filtered_df[measure_col].sum() / len(filtered_df)
    #             fig.add_trace(go.Bar(
    #                 x=filtered_df['AssignmentName'],
    #                 #               y=filtered_df[measure_col],
    #                 y=[val],
    #                 name=subcat,
    #                 marker_color=colors[color_index % len(colors)]
    #             ))
    #             color_index += 1
    #
    #         fig.update_layout(
    #             barmode='group',
    #             title='Attainment by Assignment and Course Outcomes',
    #             xaxis_title=dim_assgn,
    #             yaxis_title=measure_col,
    #             legend_title=dim_assgn
    #         )
    #         return fig
    #     # else:
    #     #     return go.Figure() # Return empty figure if group not found (shouldn't happen with our data)
    #
    #
    # @gu_app.callback(
    #     Output('course-co-bar-chart', 'figure'),
    #     [Input('course-selector', 'value'),
    #      Input('intermediate-master-school-id', 'data'),
    #      Input('school-filter-dropdown-co', 'value'),
    #      Input('department-filter-dropdown-co', 'value'),
    #      Input('program-filter-dropdown-co', 'value')]
    # )
    # def update_course_co_chart(selected_group, master_school_id, selected_schools, selected_departments, selected_programs):
    #     if selected_group == 'no_chart':
    #         raise PreventUpdate
    #         return {  # Return empty figure if 'No Chart' is selected
    #             "layout": {
    #                 "xaxis": {"visible": False},
    #                 "yaxis": {"visible": False},
    #                 "annotations": [
    #                     {
    #                         "text": str(selected_group),
    #                         "xref": "paper",
    #                         "yref": "paper",
    #                         "showarrow": False,
    #                         "font": {"size": 20}
    #                     }
    #                 ]
    #             }
    #         }
    #     elif selected_group in courses_df['CourseId'].values:
    #         course_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
    #                                          selected_programs)
    #         course_df = course_df[(course_df['CourseId'] == selected_group) & (course_df['COAttainmentLevel'] == 0)]
    #         subcat_co = course_df['COName'].unique()
    #
    #         fig = go.Figure()
    #         color_index = 0
    #         for subcat in subcat_co:
    #             filtered_df = course_df[course_df['COName'] == subcat]
    #             val = filtered_df[measure_col].sum() / len(filtered_df)
    #             fig.add_trace(go.Bar(
    #                 x=filtered_df['COName'],
    #                 #               y=filtered_df[measure_col],
    #                 y=[val],
    #                 name=subcat,
    #                 marker_color=colors[color_index % len(colors)]
    #             ))
    #             color_index += 1
    #
    #         fig.update_layout(
    #             barmode='group',
    #             title='Attainment by Course Outcomes',
    #             xaxis_title=dim_coname,
    #             yaxis_title=measure_col,
    #             legend_title=dim_coname
    #         )
    #         return fig
    #     else:
    #         return go.Figure()  # Return empty figure if group not found (shouldn't happen with our data)
    #
    #
    # # Callback to update drilldown chart based on click in the main bar chart
    # @gu_app.callback(
    #     Output('sect-assgn-co-attainment-chart', 'figure'),
    #     [Input('course-bar-chart', 'clickData'),
    #      Input('intermediate-master-school-id', 'data'),
    #      Input('school-filter-dropdown-co', 'value'),
    #      Input('department-filter-dropdown-co', 'value'),
    #      Input('program-filter-dropdown-co', 'value')]
    # )
    # def update_sect_assgn_co_chart(clickData, master_school_id, selected_schools, selected_departments, selected_programs):
    #     if clickData:
    #         clicked_sectionId = clickData['points'][0]['x']  # Get the category name from the clicked bar
    #         if clicked_sectionId in co_df['CourseId']:
    #             section_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
    #                                               selected_programs)
    #             section_df = section_df[
    #                 (section_df['SectionId'] == clicked_sectionId) & (section_df['COAttainmentLevel'] == 0)]
    #             subcat_co = section_df['COName'].unique()
    #             clicked_sectionName = section_df['SectionName'].unique()[0]
    #             clicked_CoId = clickData['points'][0]['curveNumber']  # Get the category name from the clicked bar
    #             clicked_co = subcat_co[clicked_CoId]  # Get the category name from the clicked bar
    #             print(f"Category is {clicked_sectionId} and subcat: {clicked_co} .")
    #             drilldown_df = section_df[section_df['COName'] == clicked_co]
    #             drilldown_figure = go.Figure(data=[go.Bar(
    #                 x=drilldown_df['AssignmentName'],
    #                 y=drilldown_df['Attainment'],
    #                 marker_color=drilldown_df['Threshold']
    #             )])
    #             drilldown_figure.update_layout(
    #                 title='Examwise Attainment for ' + clicked_sectionName + ' and ' + clicked_co,
    #                 xaxis_title="Assignment",
    #                 yaxis_title="Attainment",
    #                 legend_title="AssignmentName"
    #             )
    #
    #             return drilldown_figure
    #         else:
    #             return {  # Return an empty figure or a message if no drilldown data
    #                 "layout": {
    #                     "xaxis": {"visible": False},
    #                     "yaxis": {"visible": False},
    #                     "annotations": [
    #                         {
    #                             "text": f"No drilldown data for {clicked_sectionId}",
    #                             "xref": "paper",
    #                             "yref": "paper",
    #                             "showarrow": False,
    #                             "font": {"size": 12}
    #                         }
    #                     ]
    #                 }
    #             }
    #     else:
    #         return go.Figure()  # Return empty figure if no bar is clicked (initially and when clicking outside bars)
    #
    #
    # # Callback to update drilldown chart based on click in the main bar chart
    # @gu_app.callback(
    #     Output('sect-student-co-attainment-chart', 'figure'),
    #     [Input('course-bar-chart', 'clickData'),
    #      Input('intermediate-master-school-id', 'data'),
    #      Input('school-filter-dropdown-co', 'value'),
    #      Input('department-filter-dropdown-co', 'value'),
    #      Input('program-filter-dropdown-co', 'value')]
    # )
    # def update_sect_student_co_chart(clickData, master_school_id, selected_schools, selected_departments,
    #                                  selected_programs):
    #     if clickData:
    #         clicked_sectionId = clickData['points'][0]['x']  # Get the category name from the clicked bar
    #         if clicked_sectionId in co_df['CourseId']:
    #             section_df = get_filtered_dataset(co_df, master_school_id, selected_schools, selected_departments,
    #                                               selected_programs)
    #             section_df = section_df[
    #                 (section_df['SectionId'] == clicked_sectionId) & (section_df['COAttainmentLevel'] == 3)]
    #             subcat_co = section_df['COName'].unique()
    #             clicked_sectionName = section_df['SectionName'].unique()[0]
    #             clicked_CoId = clickData['points'][0]['curveNumber']  # Get the category name from the clicked bar
    #             clicked_co = subcat_co[clicked_CoId]  # Get the category name from the clicked bar
    #             print(f"Category is {clicked_sectionId} and subcat: {clicked_co} .")
    #             drilldown_df = section_df[section_df['COName'] == clicked_co]
    #             drilldown_figure = go.Figure(data=[go.Bar(
    #                 x=drilldown_df['StudentName'],
    #                 y=drilldown_df['Attainment'],
    #                 marker_color=drilldown_df['Threshold']
    #             )])
    #             drilldown_figure.update_layout(
    #                 title='Studentwise Attainment for ' + clicked_sectionName + ' and ' + clicked_co,
    #                 xaxis_title="Student",
    #                 yaxis_title="Attainment",
    #                 legend_title="Students"
    #             )
    #
    #             return drilldown_figure
    #         else:
    #             return {  # Return an empty figure or a message if no drilldown data
    #                 "layout": {
    #                     "xaxis": {"visible": False},
    #                     "yaxis": {"visible": False},
    #                     "annotations": [
    #                         {
    #                             "text": f"No drilldown data for {clicked_sectionId}",
    #                             "xref": "paper",
    #                             "yref": "paper",
    #                             "showarrow": False,
    #                             "font": {"size": 12}
    #                         }
    #                     ]
    #                 }
    #             }
    #     else:
    #         return go.Figure()  # Return empty figure if no bar is clicked (initially and when clicking outside bars)

    # --- END - Dash Callbacks for charts ---
    # --------------------------------

    return gu_app
