import os
from flask import Flask, redirect, request, url_for
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, current_user, login_required, login_user, logout_user

# Import the Dash apps
from gu_app import create_gu_app
from inpods_app import create_inpods_app


# Create a User class with first and last names
class User(UserMixin):
    def __init__(self, id, first_name, last_name):
        self.id = id
        self.first_name = first_name
        self.last_name = last_name

    @staticmethod
    def get(user_id):
        # Hardcoded user data with first and last names
        users = {
            '<EMAIL>': User('<EMAIL>', 'VC', ''),
            '<EMAIL>': User('<EMAIL>', 'CSE', 'Team')

        }
        return users.get(user_id)


# Initialize Flask app
server = Flask(__name__)
server.secret_key = os.urandom(24)  # Set a secret key for session

# Setup Flask-Login
login_manager = LoginManager()
login_manager.init_app(server)
login_manager.login_view = '/login'


@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)


# Create the Dash apps
gu_app = create_gu_app(server)
app = create_inpods_app(server)


# Define login page route
@server.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        # Redirect based on user
        if current_user.id == '<EMAIL>':
            return redirect(url_for('dashboard'))
        elif current_user.id == '<EMAIL>':
            return redirect(url_for('inpods_dashboard'))

    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']

        # Simple authentication (in a real app, you would check against a database)
        if email in ['<EMAIL>', '<EMAIL>'] and password == 'password':
            user = User.get(email)
            login_user(user)

            # Redirect based on user
            if email == '<EMAIL>':
                return redirect(url_for('dashboard'))
            elif email == '<EMAIL>':
                return redirect(url_for('inpods_dashboard'))

        return '''
        <p>Invalid login. <a href="/login">Try again</a></p>
        '''

    return '''
    <form method="post" style="max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ccc; border-radius: 5px;">
        <h1 style="text-align: center;">Login to access the dashboard</h1>
        <p><input type="email" name="email" placeholder="Email" style="width: 100%; padding: 10px; margin-bottom: 10px; border-radius: 4px; border: 1px solid #ccc;"></p>
        <p><input type="password" name="password" placeholder="Password" style="width: 100%; padding: 10px; margin-bottom: 10px; border-radius: 4px; border: 1px solid #ccc;"></p>
        <p><input type="submit" value="Login" style="width: 100%; padding: 10px; background-color: #5cb85c; color: white; border: none; border-radius: 4px; cursor: pointer;"></p>
        <p style="text-align: center; font-size: 0.8em; color: #666;">Use vc@gu.<NAME_EMAIL> with password "password"</p>
    </form>
    '''


# Protect Dash routes at the Flask level
@server.before_request
def before_request_func():
    if request.path.startswith('/dashboard/') and not current_user.is_authenticated:
        return redirect('/login')
    if request.path.startswith('/inpods_dashboard/') and not current_user.is_authenticated:
        return redirect('/login')

    # Also check if the user is trying to access the wrong dashboard
    if request.path.startswith(
            '/dashboard/') and current_user.is_authenticated and current_user.id != '<EMAIL>':
        return redirect('/inpods_dashboard/')
    if request.path.startswith(
            '/inpods_dashboard/') and current_user.is_authenticated and current_user.id != '<EMAIL>':
        return redirect('/dashboard/')


# Define dashboard route for user1 (protected)
@server.route('/dashboard/')
@login_required
def dashboard():
    # Only allow user1 to access this dashboard
    if current_user.id != '<EMAIL>':
        return redirect(url_for('dashboard'))
    return gu_app.index()


# Define dashboard route for user2 (protected)
@server.route('/inpods_dashboard/')
@login_required
def inpods_dashboard():
    # Only allow user2 to access this dashboard
    if current_user.id != '<EMAIL>':
        return redirect(url_for('inpods_dashboard'))
    return app.index()


# Redirect root to login
@server.route('/')
def index():
    if current_user.is_authenticated:
        # Redirect based on user
        if current_user.id == '<EMAIL>':
            return redirect(url_for('dashboard'))
        elif current_user.id == '<EMAIL>':
            return redirect(url_for('inpods_dashboard'))
    return redirect(url_for('login'))


if __name__ == '__main__':
    server.run(debug=True)
