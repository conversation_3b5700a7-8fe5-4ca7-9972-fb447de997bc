{"cells": [{"cell_type": "code", "execution_count": 115, "id": "855a9ce2-46a3-4c98-ba63-22e095a4bbf3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from numpy import nansum\n", "from numpy import nanmean\n", "#import matplotlib.pyplot as plt\n", "#import altair as alt\n", "import plotly.express as px\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": 117, "id": "76a5e567-5939-417f-b106-93f9d8253de6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Id  COAttainmentLevel  SchoolId             SchoolName  DepartmentId  \\\n", "0   1                  2         1  School of Agriculture             6   \n", "1   2                  2         1  School of Agriculture             6   \n", "2   3                  2         1  School of Agriculture             6   \n", "3   4                  2         1  School of Agriculture             6   \n", "4   5                  2         1  School of Agriculture             6   \n", "\n", "              DepartmentName  ProgramId  \\\n", "0  Department of Agriculture         23   \n", "1  Department of Agriculture         23   \n", "2  Department of Agriculture         23   \n", "3  Department of Agriculture         23   \n", "4  Department of Agriculture         23   \n", "\n", "                                  ProgramName  BatchId  \\\n", "0  Bachelor of Science Honours in Agriculture      275   \n", "1  Bachelor of Science Honours in Agriculture      275   \n", "2  Bachelor of Science Honours in Agriculture      275   \n", "3  Bachelor of Science Honours in Agriculture      275   \n", "4  Bachelor of Science Honours in Agriculture      275   \n", "\n", "                    BatchName  ...  Attained MasterSchoolId  \\\n", "0  <PERSON><PERSON>Sc <PERSON> - 2023-27  ...         0            294   \n", "1  <PERSON>.Sc Agriculture - 2023-27  ...         0            294   \n", "2  <PERSON><PERSON>Sc Agriculture - 2023-27  ...         1            294   \n", "3  <PERSON><PERSON>Sc Agriculture - 2023-27  ...         1            294   \n", "4  <PERSON><PERSON>Sc Agriculture - 2023-27  ...         0            294   \n", "\n", "    MasterSchoolName  POId  POName PODesc POAttDWOAL POAttDIWOAL POAttDWAL  \\\n", "0  galgotiasuni_prod  NULL    NULL   NULL       NULL        NULL      NULL   \n", "1  galgotiasuni_prod  NULL    NULL   NULL       NULL        NULL      NULL   \n", "2  galgotiasuni_prod  NULL    NULL   NULL       NULL        NULL      NULL   \n", "3  galgotiasuni_prod  NULL    NULL   NULL       NULL        NULL      NULL   \n", "4  galgotiasuni_prod  NULL    NULL   NULL       NULL        NULL      NULL   \n", "\n", "  POAttDIWAL  \n", "0       NULL  \n", "1       NULL  \n", "2       NULL  \n", "3       NULL  \n", "4       NULL  \n", "\n", "[5 rows x 36 columns]\n"]}], "source": ["def get_outcomes_from_csv(filename):\n", "    dtypes = {\n", "        \"Attainmemt\": 'float',\n", "        \"Target\": 'float',\n", "        \"Threshold\": 'float',\n", "        \"Attained\" : 'int',\n", "        \"Attained\" : 'int'\n", "    }\n", "    outcomes_df = pd.read_csv(filename,  encoding='utf8',  low_memory=False, skipinitialspace = True,\n", "                           keep_default_na=False) \n", "    return(outcomes_df)\n", "\n", "outcomes_df = get_outcomes_from_csv(\"GU_CO_attainment.csv\")\n", "print(outcomes_df.head())"]}, {"cell_type": "code", "execution_count": 119, "id": "adf4381a-e68b-474a-b382-a86bed8281a0", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       SchoolId                             SchoolName  CourseId  \\\n", "0             1                  School of Agriculture      2519   \n", "1             1                  School of Agriculture      2519   \n", "2             1                  School of Agriculture      2519   \n", "3             1                  School of Agriculture      2519   \n", "4             1                  School of Agriculture      2519   \n", "...         ...                                    ...       ...   \n", "24994         9  School of Medical and Allied Sciences      1279   \n", "24995         9  School of Medical and Allied Sciences      1279   \n", "24996         9  School of Medical and Allied Sciences      1279   \n", "24998         9  School of Medical and Allied Sciences      1279   \n", "24999         9  School of Medical and Allied Sciences      1279   \n", "\n", "                                              CourseName        COName  \\\n", "0      B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO4   \n", "1      B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO5   \n", "2      B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO6   \n", "3      B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO1   \n", "4      B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO2   \n", "...                                                  ...           ...   \n", "24994  M.Opto - 2022 - Sem III - MOPT3005 - Advanced ...  MOPT3005_CO2   \n", "24995  M.Opto - 2022 - Sem III - MOPT3005 - Advanced ...  MOPT3005_CO2   \n", "24996  M.Opto - 2022 - Sem III - MOPT3005 - Advanced ...  MOPT3005_CO2   \n", "24998  M.Opto - 2022 - Sem III - MOPT3005 - Advanced ...  MOPT3005_CO3   \n", "24999  M.Opto - 2022 - Sem III - MOPT3005 - Advanced ...  MOPT3005_CO3   \n", "\n", "       Attainment  Threshold  \n", "0        25.94502         55  \n", "1        38.06034         55  \n", "2        63.50575         55  \n", "3        57.49129         55  \n", "4        35.20570         55  \n", "...           ...        ...  \n", "24994    40.00000         55  \n", "24995    50.00000         55  \n", "24996    80.00000         55  \n", "24998    74.07407         55  \n", "24999    51.85185         55  \n", "\n", "[10410 rows x 7 columns]\n", "      SchoolId             SchoolName  CourseId  \\\n", "0            1  School of Agriculture      2519   \n", "1            1  School of Agriculture      2519   \n", "2            1  School of Agriculture      2519   \n", "3            1  School of Agriculture      2519   \n", "4            1  School of Agriculture      2519   \n", "...        ...                    ...       ...   \n", "1129        14  School of Engineering     11462   \n", "1130        14  School of Engineering     11462   \n", "1131        14  School of Engineering     11462   \n", "1132        14  School of Engineering     11462   \n", "1133        14  School of Engineering     11462   \n", "\n", "                                             CourseName        COName  \\\n", "0     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO1   \n", "1     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO2   \n", "2     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO3   \n", "3     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO4   \n", "4     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO5   \n", "...                                                 ...           ...   \n", "1129  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO1   \n", "1130  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO2   \n", "1131  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO3   \n", "1132  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO4   \n", "1133  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO5   \n", "\n", "      Attainment  Threshold  \n", "0      59.759940       55.0  \n", "1      36.793147       55.0  \n", "2      29.798393       55.0  \n", "3      26.991407       55.0  \n", "4      46.915173       55.0  \n", "...          ...        ...  \n", "1129   41.482970       55.0  \n", "1130   35.649200       55.0  \n", "1131   56.458330       55.0  \n", "1132   60.349130       55.0  \n", "1133   65.463920       55.0  \n", "\n", "[1134 rows x 7 columns]\n", "      SchoolId             SchoolName  CourseId  \\\n", "0            1  School of Agriculture      2519   \n", "1            1  School of Agriculture      2519   \n", "2            1  School of Agriculture      2519   \n", "3            1  School of Agriculture      2519   \n", "4            1  School of Agriculture      2519   \n", "...        ...                    ...       ...   \n", "1129        14  School of Engineering     11462   \n", "1130        14  School of Engineering     11462   \n", "1131        14  School of Engineering     11462   \n", "1132        14  School of Engineering     11462   \n", "1133        14  School of Engineering     11462   \n", "\n", "                                             CourseName        COName  \\\n", "0     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO1   \n", "1     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO2   \n", "2     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO3   \n", "3     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO4   \n", "4     B.Sc H Agri - 2023 - Sem I - A1UA102B - Fundam...  A1UA102B_CO5   \n", "...                                                 ...           ...   \n", "1129  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO1   \n", "1130  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO2   \n", "1131  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO3   \n", "1132  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO4   \n", "1133  B.Tech ME - 2021 - Sem VI - G3UB602B - Dynamic...  G3UB602B_CO5   \n", "\n", "      Attainment  Threshold  Attained  NotAttained  \n", "0      59.759940       55.0      True        False  \n", "1      36.793147       55.0     False         True  \n", "2      29.798393       55.0     False         True  \n", "3      26.991407       55.0     False         True  \n", "4      46.915173       55.0     False         True  \n", "...          ...        ...       ...          ...  \n", "1129   41.482970       55.0     False         True  \n", "1130   35.649200       55.0     False         True  \n", "1131   56.458330       55.0      True        False  \n", "1132   60.349130       55.0      True        False  \n", "1133   65.463920       55.0      True        False  \n", "\n", "[1134 rows x 9 columns]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SchoolId</th>\n", "      <th>SchoolName</th>\n", "      <th>Attained</th>\n", "      <th>NotAttained</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>School of Agriculture</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>School of Biological and Life Sciences</td>\n", "      <td>154</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>School of Education</td>\n", "      <td>36</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7</td>\n", "      <td>School of Hospitality</td>\n", "      <td>33</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>School of Law</td>\n", "      <td>131</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>9</td>\n", "      <td>School of Medical and Allied Sciences</td>\n", "      <td>57</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>10</td>\n", "      <td>School of Liberal Education</td>\n", "      <td>188</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>14</td>\n", "      <td>School of Engineering</td>\n", "      <td>163</td>\n", "      <td>148</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SchoolId                              SchoolName  Attained  NotAttained\n", "0         1                   School of Agriculture         2           10\n", "1         3  School of Biological and Life Sciences       154           26\n", "2         5                     School of Education        36            9\n", "3         7                   School of Hospitality        33           38\n", "4         8                           School of Law       131           46\n", "5         9   School of Medical and Allied Sciences        57           17\n", "6        10             School of Liberal Education       188           76\n", "7        14                   School of Engineering       163          148"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["def build_level2_df(df):\n", "    temp_df = df[df['COAttainmentLevel'] == 2][['SchoolId', 'SchoolName', 'CourseId', 'CourseName',  'COName', 'Attainment', 'Threshold']].drop_duplicates()\n", "    print(temp_df)\n", "    level2_df = temp_df.groupby(['SchoolId', 'SchoolName', 'CourseId', 'CourseName', 'COName']).mean(numeric_only=True).reset_index()    \n", "    print(level2_df)\n", "    level2_df['Attained'] = level2_df['Attainment'] >= level2_df['Threshold']\n", "    level2_df['NotAttained'] = level2_df['Attainment'] < level2_df['Threshold']\n", "    print(level2_df)\n", "    level2_df = level2_df[['SchoolId', 'SchoolName','Attained', 'NotAttained']].groupby(['SchoolId', 'SchoolName']).sum(numeric_only=True).reset_index()\n", "    return level2_df\n", "\n", "def get_schoolwise_attainment_chart(level2_df):\n", "    fig = px.bar(level2_df, x=\"SchoolName\", y=[\"Attained\",'NotAttained'], labels={\"Attained\": \"Attained\", \"NotAttained\": \"Not Attained\"})\n", "    return fig\n", "\n", "def get_schoolwise_attainment_100chart(level2_df):\n", "    level2_df_copy = level2_df.copy() \n", "    level2_df_copy['Attained_pct'] = level2_df_copy['Attained'] / (level2_df_copy['Attained'] + level2_df_copy['NotAttained']) * 100    \n", "    level2_df_copy['NotAttained_pct'] = level2_df_copy['NotAttained'] / (level2_df_copy['Attained'] + level2_df_copy['NotAttained']) * 100    \n", "    fig = px.bar(level2_df_copy, x=\"SchoolName\", y=[\"Attained_pct\",'NotAttained_pct'])\n", "    return fig\n", "\n", "\n", "level2_df = build_level2_df(outcomes_df)\n", "level2_df"]}, {"cell_type": "code", "execution_count": 121, "id": "085e2fcc-6b2c-4078-93a3-a6ed28371ed4", "metadata": {}, "outputs": [], "source": ["schoolwise_fig = get_schoolwise_attainment_chart(level2_df)\n", "schoolwise_100fig = get_schoolwise_attainment_100chart(level2_df    )\n"]}, {"cell_type": "code", "execution_count": 123, "id": "5370301e-af52-470d-99b8-d2bab6d5dac2", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "hovertemplate": "variable=Attained<br>SchoolName=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "Attained", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Attained", "offsetgroup": "Attained", "orientation": "v", "showlegend": true, "textposition": "auto", "type": "bar", "x": ["School of Agriculture", "School of Biological and Life Sciences", "School of Education", "School of Hospitality", "School of Law", "School of Medical and Allied Sciences", "School of Liberal Education", "School of Engineering"], "xaxis": "x", "y": [2, 154, 36, 33, 131, 57, 188, 163], "yaxis": "y"}, {"alignmentgroup": "True", "hovertemplate": "variable=NotAttained<br>SchoolName=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "NotAttained", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "NotAttained", "offsetgroup": "NotAttained", "orientation": "v", "showlegend": true, "textposition": "auto", "type": "bar", "x": ["School of Agriculture", "School of Biological and Life Sciences", "School of Education", "School of Hospitality", "School of Law", "School of Medical and Allied Sciences", "School of Liberal Education", "School of Engineering"], "xaxis": "x", "y": [10, 26, 9, 38, 46, 17, 76, 148], "yaxis": "y"}], "layout": {"autosize": true, "barmode": "relative", "legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "autorange": true, "domain": [0, 1], "range": [-0.5, 7.5], "title": {"text": "SchoolName"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 327.36842105263156], "title": {"text": "value"}, "type": "linear"}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"1ba5578e-c480-40bd-be88-52395a8edd6d\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"1ba5578e-c480-40bd-be88-52395a8edd6d\")) {                    Plotly.newPlot(                        \"1ba5578e-c480-40bd-be88-52395a8edd6d\",                        [{\"alignmentgroup\":\"True\",\"hovertemplate\":\"variable=Attained<br>SchoolName=%{x}<br>value=%{y}<extra></extra>\",\"legendgroup\":\"Attained\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Attained\",\"offsetgroup\":\"Attained\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"x\":[\"School of Agriculture\",\"School of Biological and Life Sciences\",\"School of Education\",\"School of Hospitality\",\"School of Law\",\"School of Medical and Allied Sciences\",\"School of Liberal Education\",\"School of Engineering\"],\"xaxis\":\"x\",\"y\":[2,154,36,33,131,57,188,163],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"hovertemplate\":\"variable=NotAttained<br>SchoolName=%{x}<br>value=%{y}<extra></extra>\",\"legendgroup\":\"NotAttained\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"NotAttained\",\"offsetgroup\":\"NotAttained\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"x\":[\"School of Agriculture\",\"School of Biological and Life Sciences\",\"School of Education\",\"School of Hospitality\",\"School of Law\",\"School of Medical and Allied Sciences\",\"School of Liberal Education\",\"School of Engineering\"],\"xaxis\":\"x\",\"y\":[10,26,9,38,46,17,76,148],\"yaxis\":\"y\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"SchoolName\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"value\"}},\"legend\":{\"title\":{\"text\":\"variable\"},\"tracegroupgap\":0},\"margin\":{\"t\":60},\"barmode\":\"relative\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('1ba5578e-c480-40bd-be88-52395a8edd6d');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["schoolwise_fig"]}, {"cell_type": "code", "execution_count": 125, "id": "0670c0d3-70a6-4b5b-a160-cbaa53350427", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "hovertemplate": "variable=Attained_pct<br>SchoolName=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "Attained_pct", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Attained_pct", "offsetgroup": "Attained_pct", "orientation": "v", "showlegend": true, "textposition": "auto", "type": "bar", "x": ["School of Agriculture", "School of Biological and Life Sciences", "School of Education", "School of Hospitality", "School of Law", "School of Medical and Allied Sciences", "School of Liberal Education", "School of Engineering"], "xaxis": "x", "y": [16.666666666666664, 85.55555555555556, 80, 46.478873239436616, 74.01129943502825, 77.02702702702703, 71.21212121212122, 52.41157556270096], "yaxis": "y"}, {"alignmentgroup": "True", "hovertemplate": "variable=NotAttained_pct<br>SchoolName=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "NotAttained_pct", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "NotAttained_pct", "offsetgroup": "NotAttained_pct", "orientation": "v", "showlegend": true, "textposition": "auto", "type": "bar", "x": ["School of Agriculture", "School of Biological and Life Sciences", "School of Education", "School of Hospitality", "School of Law", "School of Medical and Allied Sciences", "School of Liberal Education", "School of Engineering"], "xaxis": "x", "y": [83.33333333333334, 14.444444444444443, 20, 53.52112676056338, 25.98870056497175, 22.972972972972975, 28.78787878787879, 47.58842443729904], "yaxis": "y"}], "layout": {"autosize": true, "barmode": "relative", "legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "autorange": true, "domain": [0, 1], "range": [-0.5, 7.5], "title": {"text": "SchoolName"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 105.26315789473685], "title": {"text": "value"}, "type": "linear"}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"44c3e95e-1c5c-4df6-89ad-e185f65fc904\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"44c3e95e-1c5c-4df6-89ad-e185f65fc904\")) {                    Plotly.newPlot(                        \"44c3e95e-1c5c-4df6-89ad-e185f65fc904\",                        [{\"alignmentgroup\":\"True\",\"hovertemplate\":\"variable=Attained_pct<br>SchoolName=%{x}<br>value=%{y}<extra></extra>\",\"legendgroup\":\"Attained_pct\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Attained_pct\",\"offsetgroup\":\"Attained_pct\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"x\":[\"School of Agriculture\",\"School of Biological and Life Sciences\",\"School of Education\",\"School of Hospitality\",\"School of Law\",\"School of Medical and Allied Sciences\",\"School of Liberal Education\",\"School of Engineering\"],\"xaxis\":\"x\",\"y\":[16.666666666666664,85.55555555555556,80.0,46.478873239436616,74.01129943502825,77.02702702702703,71.21212121212122,52.41157556270096],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"hovertemplate\":\"variable=NotAttained_pct<br>SchoolName=%{x}<br>value=%{y}<extra></extra>\",\"legendgroup\":\"NotAttained_pct\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"NotAttained_pct\",\"offsetgroup\":\"NotAttained_pct\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"x\":[\"School of Agriculture\",\"School of Biological and Life Sciences\",\"School of Education\",\"School of Hospitality\",\"School of Law\",\"School of Medical and Allied Sciences\",\"School of Liberal Education\",\"School of Engineering\"],\"xaxis\":\"x\",\"y\":[83.33333333333334,14.444444444444443,20.0,53.52112676056338,25.98870056497175,22.972972972972975,28.78787878787879,47.58842443729904],\"yaxis\":\"y\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"SchoolName\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"value\"}},\"legend\":{\"title\":{\"text\":\"variable\"},\"tracegroupgap\":0},\"margin\":{\"t\":60},\"barmode\":\"relative\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('44c3e95e-1c5c-4df6-89ad-e185f65fc904');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["schoolwise_100fig"]}, {"cell_type": "code", "execution_count": 165, "id": "14e26ffb-01bb-46e9-ba6c-234e15385e2f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>bucket</th>\n", "      <th>0-20</th>\n", "      <th>21-40</th>\n", "      <th>41-60</th>\n", "      <th>61-80</th>\n", "      <th>81-100</th>\n", "    </tr>\n", "    <tr>\n", "      <th>SchoolName</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>School of Agriculture</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Biological and Life Sciences</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>26.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Education</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Engineering</th>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>36.0</td>\n", "      <td>28.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Hospitality</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.0</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Law</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>16.0</td>\n", "      <td>20.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Liberal Education</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>27.0</td>\n", "      <td>26.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>School of Medical and Allied Sciences</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.0</td>\n", "      <td>15.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["bucket                                  0-20  21-40  41-60  61-80  81-100\n", "SchoolName                                                               \n", "School of Agriculture                    0.0    0.0    2.0    0.0     0.0\n", "School of Biological and Life Sciences   0.0    0.0    6.0   26.0     4.0\n", "School of Education                      0.0    0.0    4.0    5.0     0.0\n", "School of Engineering                    0.0    5.0   36.0   28.0     0.0\n", "School of Hospitality                    0.0    0.0   13.0    3.0     5.0\n", "School of Law                            1.0    0.0   16.0   20.0     0.0\n", "School of Liberal Education              0.0    0.0   27.0   26.0     3.0\n", "School of Medical and Allied Sciences    0.0    0.0    9.0   15.0     0.0"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_schoolwise_attainment_distr(df):\n", "    filtered_df = df[df['COAttainmentLevel'] == 2][['SchoolId', 'SchoolName', 'CourseId', 'CourseName',  'COName', 'Attainment']].drop_duplicates()\n", "    avg_attainment_df = filtered_df.groupby(['SchoolId', 'SchoolName', 'CourseId', 'CourseName']).mean(numeric_only=True).reset_index() \n", "    # Define bins and labels\n", "    bins = [0, 20, 40, 60, 80, 100]\n", "    labels = ['0-20', '21-40', '41-60', '61-80', '81-100']\n", "    # Create buckets using pd.cut()\n", "    avg_attainment_df['bucket'] = pd.cut(avg_attainment_df['Attainment'], bins=bins, labels=labels, right=False)\n", "    attainment_buckets_df = avg_attainment_df[['SchoolId', 'SchoolName', 'bucket']]\n", "    #count bucket occurance\n", "    attainment_buckets_df['count'] = 1\n", "    schoolwise_buckets_distr_df = attainment_buckets_df.groupby(['SchoolId', 'SchoolName', 'bucket'], observed=True).count().reset_index()\n", "    schoolwise_buckets_distr_df = schoolwise_buckets_distr_df.pivot_table('count', ['SchoolName'], 'bucket').fillna(0)\n", "    return schoolwise_buckets_distr_df\n", "schoolwise_distr_df = get_schoolwise_attainment_distr(outcomes_df)\n", "schoolwise_distr_df"]}, {"cell_type": "code", "execution_count": 107, "id": "769d4e09-e033-4a31-8c05-9a68b3d5d200", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SchoolId</th>\n", "      <th>SchoolName</th>\n", "      <th>bucket</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Chetana's Institute of Management and Research</td>\n", "      <td>0-20</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Chetana's Institute of Management and Research</td>\n", "      <td>41-60</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Chetana's Institute of Management and Research</td>\n", "      <td>61-80</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>Chetana's Institute of Management and Research</td>\n", "      <td>81-100</td>\n", "      <td>14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SchoolId                                      SchoolName  bucket  count\n", "0         1  Chetana's Institute of Management and Research    0-20      1\n", "1         1  Chetana's Institute of Management and Research   41-60      3\n", "2         1  Chetana's Institute of Management and Research   61-80      4\n", "3         1  Chetana's Institute of Management and Research  81-100     14"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["from dash import dash_table\n", "dash_table.DataTable(\n", "    id='table',\n", "    columns=[{\"name\": i, \"id\": i} for i in df.columns],\n", "    data=df.to_dict('records'),\n", ")"]}, {"cell_type": "code", "execution_count": 103, "id": "7c7883f8-002d-4e51-b525-67c2e12a8062", "metadata": {}, "outputs": [{"data": {"text/plain": ["SchoolId         int64\n", "SchoolName      object\n", "bucket        category\n", "count            int64\n", "dtype: object"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["schoolwise_distr_df.dtypes"]}, {"cell_type": "code", "execution_count": 109, "id": "1051451e-9690-485f-9171-d864999925af", "metadata": {}, "outputs": [], "source": ["counts_df = schoolwise_distr_df.pivot_table('count', ['SchoolName'], 'bucket')"]}, {"cell_type": "code", "execution_count": 111, "id": "144d7251-f78b-43ad-8e75-922d965358cb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>bucket</th>\n", "      <th>0-20</th>\n", "      <th>41-60</th>\n", "      <th>61-80</th>\n", "      <th>81-100</th>\n", "    </tr>\n", "    <tr>\n", "      <th>SchoolName</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Chetana's Institute of Management and Research</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["bucket                                          0-20  41-60  61-80  81-100\n", "SchoolName                                                                \n", "Chetana's Institute of Management and Research   1.0    1.0    1.0     1.0"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["counts_df"]}, {"cell_type": "code", "execution_count": null, "id": "19b3ebce-2ae9-4b89-86db-44b8de7ee1b2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}